package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// JobInstance represents an execution instance of a job
type JobInstance struct {
	ID                  uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	JobDefinitionID     *uuid.UUID `json:"job_definition_id" gorm:"type:uuid"`
	WorkflowInstanceID  *uuid.UUID `json:"workflow_instance_id" gorm:"type:uuid"`
	ScheduleID          *uuid.UUID `json:"schedule_id" gorm:"type:uuid"`
	ExecutionNodeID     *uuid.UUID `json:"execution_node_id" gorm:"type:uuid"`
	Status              string    `json:"status" gorm:"default:'pending'"` // pending, running, success, failed, cancelled, timeout
	Priority            int       `json:"priority" gorm:"default:100"`
	ScheduledAt         time.Time `json:"scheduled_at" gorm:"not null"`
	StartedAt           *time.Time `json:"started_at"`
	CompletedAt         *time.Time `json:"completed_at"`
	ExitCode            *int      `json:"exit_code"`
	OutputLog           *string   `json:"output_log"`
	ErrorLog            *string   `json:"error_log"`
	Parameters          map[string]interface{} `json:"parameters" gorm:"type:jsonb;default:'{}'"`
	EnvironmentVariables map[string]string `json:"environment_variables" gorm:"type:jsonb;default:'{}'"`
	ResourceUsage       map[string]interface{} `json:"resource_usage" gorm:"type:jsonb;default:'{}'"`
	RetryAttempt        int       `json:"retry_attempt" gorm:"default:0"`
	ParentInstanceID    *uuid.UUID `json:"parent_instance_id" gorm:"type:uuid"`
	CreatedAt           time.Time `json:"created_at"`
	UpdatedAt           time.Time `json:"updated_at"`

	// Associations
	JobDefinition     *JobDefinition     `json:"job_definition" gorm:"foreignKey:JobDefinitionID"`
	WorkflowInstance  *WorkflowInstance  `json:"workflow_instance" gorm:"foreignKey:WorkflowInstanceID"`
	Schedule          *Schedule          `json:"schedule" gorm:"foreignKey:ScheduleID"`
	ExecutionNode     *ExecutionNode     `json:"execution_node" gorm:"foreignKey:ExecutionNodeID"`
	ParentInstance    *JobInstance       `json:"parent_instance" gorm:"foreignKey:ParentInstanceID"`
	ChildInstances    []JobInstance      `json:"child_instances" gorm:"foreignKey:ParentInstanceID"`
}

// Workflow represents a workflow definition
type Workflow struct {
	ID          uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	Name        string    `json:"name" gorm:"not null"`
	Description *string   `json:"description"`
	Definition  map[string]interface{} `json:"definition" gorm:"type:jsonb;not null"` // Workflow DAG definition
	Version     int       `json:"version" gorm:"default:1"`
	IsActive    bool      `json:"is_active" gorm:"default:true"`
	OwnerID     *uuid.UUID `json:"owner_id" gorm:"type:uuid"`
	Tags        []string  `json:"tags" gorm:"type:jsonb;default:'[]'"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`

	// Associations
	Owner             *User               `json:"owner" gorm:"foreignKey:OwnerID"`
	WorkflowInstances []WorkflowInstance  `json:"workflow_instances" gorm:"foreignKey:WorkflowID"`
	SLADefinitions    []SLADefinition     `json:"sla_definitions" gorm:"foreignKey:WorkflowID"`
}

// WorkflowInstance represents an execution instance of a workflow
type WorkflowInstance struct {
	ID               uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	WorkflowID       uuid.UUID `json:"workflow_id" gorm:"type:uuid;not null"`
	Status           string    `json:"status" gorm:"default:'pending'"` // pending, running, success, failed, cancelled
	ScheduledAt      time.Time `json:"scheduled_at" gorm:"not null"`
	StartedAt        *time.Time `json:"started_at"`
	CompletedAt      *time.Time `json:"completed_at"`
	Parameters       map[string]interface{} `json:"parameters" gorm:"type:jsonb;default:'{}'"`
	ExecutionContext map[string]interface{} `json:"execution_context" gorm:"type:jsonb;default:'{}'"`
	CreatedBy        *uuid.UUID `json:"created_by" gorm:"type:uuid"`
	CreatedAt        time.Time `json:"created_at"`
	UpdatedAt        time.Time `json:"updated_at"`

	// Associations
	Workflow     *Workflow     `json:"workflow" gorm:"foreignKey:WorkflowID"`
	Creator      *User         `json:"creator" gorm:"foreignKey:CreatedBy"`
	JobInstances []JobInstance `json:"job_instances" gorm:"foreignKey:WorkflowInstanceID"`
}

// JobDependency represents dependencies between jobs
type JobDependency struct {
	ID                  uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	ParentJobID         uuid.UUID `json:"parent_job_id" gorm:"type:uuid;not null"`
	ChildJobID          uuid.UUID `json:"child_job_id" gorm:"type:uuid;not null"`
	DependencyType      string    `json:"dependency_type" gorm:"default:'success'"` // success, failure, completion, always
	ConditionExpression *string   `json:"condition_expression"` // Optional condition for complex dependencies
	CreatedAt           time.Time `json:"created_at"`

	// Associations
	ParentJob *JobDefinition `json:"parent_job" gorm:"foreignKey:ParentJobID"`
	ChildJob  *JobDefinition `json:"child_job" gorm:"foreignKey:ChildJobID"`
}

// Event represents system events that can trigger jobs
type Event struct {
	ID          uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	EventType   string    `json:"event_type" gorm:"not null"` // file_arrival, database_change, api_call, schedule, manual
	Source      string    `json:"source" gorm:"not null"`
	Payload     map[string]interface{} `json:"payload" gorm:"type:jsonb;default:'{}'"`
	Processed   bool      `json:"processed" gorm:"default:false"`
	ProcessedAt *time.Time `json:"processed_at"`
	CreatedAt   time.Time `json:"created_at"`
}

// EventTrigger represents event-based job triggers
type EventTrigger struct {
	ID                  uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	JobDefinitionID     uuid.UUID `json:"job_definition_id" gorm:"type:uuid;not null"`
	EventType           string    `json:"event_type" gorm:"not null"`
	ConditionExpression *string   `json:"condition_expression"`
	IsActive            bool      `json:"is_active" gorm:"default:true"`
	CreatedAt           time.Time `json:"created_at"`
	UpdatedAt           time.Time `json:"updated_at"`

	// Associations
	JobDefinition *JobDefinition `json:"job_definition" gorm:"foreignKey:JobDefinitionID"`
}

// SLADefinition represents Service Level Agreement definitions
type SLADefinition struct {
	ID                     uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	Name                   string    `json:"name" gorm:"not null"`
	JobDefinitionID        *uuid.UUID `json:"job_definition_id" gorm:"type:uuid"`
	WorkflowID             *uuid.UUID `json:"workflow_id" gorm:"type:uuid"`
	MaxDurationSeconds     *int      `json:"max_duration_seconds"`
	MaxRetryCount          *int      `json:"max_retry_count"`
	SuccessRateThreshold   *float64  `json:"success_rate_threshold"` // Percentage
	NotificationRules      []map[string]interface{} `json:"notification_rules" gorm:"type:jsonb;default:'[]'"`
	IsActive               bool      `json:"is_active" gorm:"default:true"`
	CreatedAt              time.Time `json:"created_at"`
	UpdatedAt              time.Time `json:"updated_at"`

	// Associations
	JobDefinition *JobDefinition `json:"job_definition" gorm:"foreignKey:JobDefinitionID"`
	Workflow      *Workflow      `json:"workflow" gorm:"foreignKey:WorkflowID"`
}

// AuditLog represents audit trail for system actions
type AuditLog struct {
	ID           uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	UserID       *uuid.UUID `json:"user_id" gorm:"type:uuid"`
	Action       string    `json:"action" gorm:"not null"`
	ResourceType string    `json:"resource_type" gorm:"not null"`
	ResourceID   *uuid.UUID `json:"resource_id" gorm:"type:uuid"`
	OldValues    map[string]interface{} `json:"old_values" gorm:"type:jsonb"`
	NewValues    map[string]interface{} `json:"new_values" gorm:"type:jsonb"`
	IPAddress    *string   `json:"ip_address"`
	UserAgent    *string   `json:"user_agent"`
	CreatedAt    time.Time `json:"created_at"`

	// Associations
	User *User `json:"user" gorm:"foreignKey:UserID"`
}

// Notification represents system notifications
type Notification struct {
	ID        uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	UserID    *uuid.UUID `json:"user_id" gorm:"type:uuid"`
	Type      string    `json:"type" gorm:"not null"` // email, slack, webhook, sms
	Title     string    `json:"title" gorm:"not null"`
	Message   string    `json:"message" gorm:"not null"`
	Status    string    `json:"status" gorm:"default:'pending'"` // pending, sent, failed
	SentAt    *time.Time `json:"sent_at"`
	Metadata  map[string]interface{} `json:"metadata" gorm:"type:jsonb;default:'{}'"`
	CreatedAt time.Time `json:"created_at"`

	// Associations
	User *User `json:"user" gorm:"foreignKey:UserID"`
}

// JobStatus constants
const (
	JobStatusPending   = "pending"
	JobStatusRunning   = "running"
	JobStatusSuccess   = "success"
	JobStatusFailed    = "failed"
	JobStatusCancelled = "cancelled"
	JobStatusTimeout   = "timeout"
)

// WorkflowStatus constants
const (
	WorkflowStatusPending   = "pending"
	WorkflowStatusRunning   = "running"
	WorkflowStatusSuccess   = "success"
	WorkflowStatusFailed    = "failed"
	WorkflowStatusCancelled = "cancelled"
)

// DependencyType constants
const (
	DependencyTypeSuccess    = "success"
	DependencyTypeFailure    = "failure"
	DependencyTypeCompletion = "completion"
	DependencyTypeAlways     = "always"
)

// EventType constants
const (
	EventTypeFileArrival    = "file_arrival"
	EventTypeDatabaseChange = "database_change"
	EventTypeAPICall        = "api_call"
	EventTypeSchedule       = "schedule"
	EventTypeManual         = "manual"
)

// NotificationType constants
const (
	NotificationTypeEmail   = "email"
	NotificationTypeSlack   = "slack"
	NotificationTypeWebhook = "webhook"
	NotificationTypeSMS     = "sms"
)

// NotificationStatus constants
const (
	NotificationStatusPending = "pending"
	NotificationStatusSent    = "sent"
	NotificationStatusFailed  = "failed"
)

// BeforeCreate hooks
func (ji *JobInstance) BeforeCreate(tx *gorm.DB) error {
	if ji.ID == uuid.Nil {
		ji.ID = uuid.New()
	}
	return nil
}

func (w *Workflow) BeforeCreate(tx *gorm.DB) error {
	if w.ID == uuid.Nil {
		w.ID = uuid.New()
	}
	return nil
}

func (wi *WorkflowInstance) BeforeCreate(tx *gorm.DB) error {
	if wi.ID == uuid.Nil {
		wi.ID = uuid.New()
	}
	return nil
}

func (jd *JobDependency) BeforeCreate(tx *gorm.DB) error {
	if jd.ID == uuid.Nil {
		jd.ID = uuid.New()
	}
	return nil
}

func (e *Event) BeforeCreate(tx *gorm.DB) error {
	if e.ID == uuid.Nil {
		e.ID = uuid.New()
	}
	return nil
}

func (et *EventTrigger) BeforeCreate(tx *gorm.DB) error {
	if et.ID == uuid.Nil {
		et.ID = uuid.New()
	}
	return nil
}

func (sla *SLADefinition) BeforeCreate(tx *gorm.DB) error {
	if sla.ID == uuid.Nil {
		sla.ID = uuid.New()
	}
	return nil
}

func (al *AuditLog) BeforeCreate(tx *gorm.DB) error {
	if al.ID == uuid.Nil {
		al.ID = uuid.New()
	}
	return nil
}

func (n *Notification) BeforeCreate(tx *gorm.DB) error {
	if n.ID == uuid.Nil {
		n.ID = uuid.New()
	}
	return nil
}
