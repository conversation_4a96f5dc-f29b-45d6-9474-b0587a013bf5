package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// JobDefinition represents a job definition with mainframe support
type JobDefinition struct {
	ID              uuid.UUID              `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	Name            string                 `json:"name" gorm:"not null;uniqueIndex"`
	Description     *string                `json:"description"`
	JobType         string                 `json:"job_type" gorm:"not null"` // shell, python, java, mainframe_jcl, mainframe_rexx, etc.
	Command         *string                `json:"command"`                  // Command to execute (for non-mainframe jobs)
	Script          *string                `json:"script"`                   // Script content
	Parameters      map[string]interface{} `json:"parameters" gorm:"type:jsonb;default:'{}'"`
	
	// Job execution settings
	Timeout         int                    `json:"timeout" gorm:"default:3600"`         // Timeout in seconds
	MaxRetries      int                    `json:"max_retries" gorm:"default:3"`        // Maximum retry attempts
	RetryDelay      int                    `json:"retry_delay" gorm:"default:60"`       // Delay between retries in seconds
	Priority        int                    `json:"priority" gorm:"default:5"`           // Job priority (1-10)
	
	// Resource requirements
	ResourcePoolID  *uuid.UUID             `json:"resource_pool_id" gorm:"type:uuid"`   // Resource pool for execution
	RequiredMemory  *int64                 `json:"required_memory"`                     // Required memory in MB
	RequiredCPU     *float64               `json:"required_cpu"`                        // Required CPU cores
	
	// Environment and variables
	Environment     map[string]string      `json:"environment" gorm:"type:jsonb;default:'{}'"`     // Environment variables
	WorkingDir      *string                `json:"working_dir"`                         // Working directory
	
	// Mainframe-specific settings
	MainframeConfig *MainframeJobConfig    `json:"mainframe_config" gorm:"type:jsonb"`  // Mainframe job configuration
	
	// Metadata
	Tags            []string               `json:"tags" gorm:"type:jsonb;default:'[]'"`             // Tags for categorization
	IsActive        bool                   `json:"is_active" gorm:"default:true"`
	CreatedAt       time.Time              `json:"created_at"`
	UpdatedAt       time.Time              `json:"updated_at"`
	CreatedBy       *uuid.UUID             `json:"created_by" gorm:"type:uuid"`
	
	// Associations
	Owner           *User                  `json:"owner" gorm:"foreignKey:CreatedBy"`
	ResourcePool    *ResourcePool          `json:"resource_pool" gorm:"foreignKey:ResourcePoolID"`
	Instances       []JobInstance          `json:"instances" gorm:"foreignKey:JobDefinitionID"`
	Schedules       []Schedule             `json:"schedules" gorm:"foreignKey:JobDefinitionID"`
	Dependencies    []JobDependency        `json:"dependencies" gorm:"foreignKey:ChildJobID"`
	Dependents      []JobDependency        `json:"dependents" gorm:"foreignKey:ParentJobID"`
	EventTriggers   []EventTrigger         `json:"event_triggers" gorm:"foreignKey:JobDefinitionID"`
	SLADefinitions  []SLADefinition        `json:"sla_definitions" gorm:"foreignKey:JobDefinitionID"`
}

// MainframeJobConfig holds mainframe-specific job configuration
type MainframeJobConfig struct {
	ConnectionID     uuid.UUID              `json:"connection_id"`                    // Mainframe connection to use
	JobClass         string                 `json:"job_class,omitempty"`              // Job class (A, B, C, etc.)
	JobType          string                 `json:"job_type"`                         // JCL, REXX, COBOL, etc.
	JCLTemplate      *string                `json:"jcl_template,omitempty"`           // JCL template content
	JCLParameters    map[string]interface{} `json:"jcl_parameters,omitempty"`         // JCL parameter substitutions
	DatasetOperations []DatasetOperation    `json:"dataset_operations,omitempty"`     // Dataset operations to perform
	DataTransfers    []DataTransferConfig   `json:"data_transfers,omitempty"`         // Data transfers to perform
	SecurityProfile  *string                `json:"security_profile,omitempty"`       // Security profile to use
	NotificationRules []NotificationRule    `json:"notification_rules,omitempty"`     // Mainframe-specific notifications
	
	// Job submission options
	SubmitOptions    map[string]interface{} `json:"submit_options,omitempty"`         // Additional submission options
	MonitoringConfig *MonitoringConfig      `json:"monitoring_config,omitempty"`      // Monitoring configuration
}

// DatasetOperation represents a dataset operation within a job
type DatasetOperation struct {
	Operation       string                 `json:"operation"`                        // allocate, delete, copy, rename, etc.
	DatasetName     string                 `json:"dataset_name"`
	MemberName      *string                `json:"member_name,omitempty"`
	DatasetType     *string                `json:"dataset_type,omitempty"`           // PS, PO, VSAM, etc.
	RecordFormat    *string                `json:"record_format,omitempty"`          // FB, VB, U
	RecordLength    *int                   `json:"record_length,omitempty"`
	BlockSize       *int                   `json:"block_size,omitempty"`
	Space           *string                `json:"space,omitempty"`                  // Space allocation
	VolumeSerial    *string                `json:"volume_serial,omitempty"`
	SourceDataset   *string                `json:"source_dataset,omitempty"`         // For copy operations
	TargetDataset   *string                `json:"target_dataset,omitempty"`         // For copy operations
	Condition       *string                `json:"condition,omitempty"`              // Conditional execution
	OnError         *string                `json:"on_error,omitempty"`               // Error handling
}

// DataTransferConfig represents a data transfer configuration
type DataTransferConfig struct {
	TransferType    string                 `json:"transfer_type"`                    // upload, download, copy
	Protocol        string                 `json:"protocol"`                         // FTP, SFTP, NFS, TCP
	SourcePath      string                 `json:"source_path"`
	DestinationPath string                 `json:"destination_path"`
	TransferMode    string                 `json:"transfer_mode,omitempty"`          // binary, ascii, ebcdic
	ConvertEncoding bool                   `json:"convert_encoding,omitempty"`
	SourceEncoding  *string                `json:"source_encoding,omitempty"`
	TargetEncoding  *string                `json:"target_encoding,omitempty"`
	Condition       *string                `json:"condition,omitempty"`              // Conditional execution
	OnError         *string                `json:"on_error,omitempty"`               // Error handling
	RetryPolicy     *RetryPolicy           `json:"retry_policy,omitempty"`           // Retry configuration
}

// NotificationRule represents a notification rule for mainframe jobs
type NotificationRule struct {
	Trigger         string                 `json:"trigger"`                          // on_start, on_success, on_failure, on_completion
	NotificationType string                `json:"notification_type"`               // email, slack, webhook, mq_message
	Recipients      []string               `json:"recipients"`
	Template        *string                `json:"template,omitempty"`
	Parameters      map[string]interface{} `json:"parameters,omitempty"`
	Condition       *string                `json:"condition,omitempty"`              // Conditional notification
}

// MonitoringConfig represents monitoring configuration for mainframe jobs
type MonitoringConfig struct {
	EnableMonitoring bool                   `json:"enable_monitoring"`
	MetricTypes      []string               `json:"metric_types,omitempty"`           // cpu, memory, job_queue, etc.
	CollectionInterval time.Duration        `json:"collection_interval,omitempty"`
	AlertRules       []AlertRule            `json:"alert_rules,omitempty"`
}

// AlertRule represents an alert rule for monitoring
type AlertRule struct {
	MetricName      string                 `json:"metric_name"`
	Condition       string                 `json:"condition"`                        // >, <, >=, <=, ==, !=
	Threshold       float64                `json:"threshold"`
	Severity        string                 `json:"severity"`                         // info, warning, critical
	NotificationChannels []string          `json:"notification_channels"`
}

// RetryPolicy represents retry configuration
type RetryPolicy struct {
	MaxAttempts     int                    `json:"max_attempts"`
	DelaySeconds    int                    `json:"delay_seconds"`
	BackoffType     string                 `json:"backoff_type"`                     // fixed, exponential, linear
	MaxDelaySeconds *int                   `json:"max_delay_seconds,omitempty"`
}

// ResourcePool represents a pool of execution resources
type ResourcePool struct {
	ID              uuid.UUID              `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	Name            string                 `json:"name" gorm:"not null;uniqueIndex"`
	Description     *string                `json:"description"`
	PoolType        string                 `json:"pool_type" gorm:"not null"`        // local, kubernetes, mainframe
	Configuration   map[string]interface{} `json:"configuration" gorm:"type:jsonb;default:'{}'"`
	MaxConcurrentJobs int                  `json:"max_concurrent_jobs" gorm:"default:10"`
	IsActive        bool                   `json:"is_active" gorm:"default:true"`
	CreatedAt       time.Time              `json:"created_at"`
	UpdatedAt       time.Time              `json:"updated_at"`
	CreatedBy       *uuid.UUID             `json:"created_by" gorm:"type:uuid"`
	
	// Associations
	Owner           *User                  `json:"owner" gorm:"foreignKey:CreatedBy"`
	JobDefinitions  []JobDefinition        `json:"job_definitions" gorm:"foreignKey:ResourcePoolID"`
	ExecutionNodes  []ExecutionNode        `json:"execution_nodes" gorm:"foreignKey:ResourcePoolID"`
}

// ExecutionNode represents a node that can execute jobs
type ExecutionNode struct {
	ID              uuid.UUID              `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	Name            string                 `json:"name" gorm:"not null"`
	NodeType        string                 `json:"node_type" gorm:"not null"`        // local, remote, mainframe
	Hostname        *string                `json:"hostname"`
	Port            *int                   `json:"port"`
	ResourcePoolID  *uuid.UUID             `json:"resource_pool_id" gorm:"type:uuid"`
	Capabilities    []string               `json:"capabilities" gorm:"type:jsonb;default:'[]'"`
	Configuration   map[string]interface{} `json:"configuration" gorm:"type:jsonb;default:'{}'"`
	Status          string                 `json:"status" gorm:"default:'active'"`   // active, inactive, maintenance
	LastHeartbeat   *time.Time             `json:"last_heartbeat"`
	MaxConcurrentJobs int                  `json:"max_concurrent_jobs" gorm:"default:5"`
	CurrentJobs     int                    `json:"current_jobs" gorm:"default:0"`
	CreatedAt       time.Time              `json:"created_at"`
	UpdatedAt       time.Time              `json:"updated_at"`
	
	// Associations
	ResourcePool    *ResourcePool          `json:"resource_pool" gorm:"foreignKey:ResourcePoolID"`
	JobInstances    []JobInstance          `json:"job_instances" gorm:"foreignKey:ExecutionNodeID"`
}

// Schedule represents a job schedule
type Schedule struct {
	ID              uuid.UUID              `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	Name            string                 `json:"name" gorm:"not null"`
	JobDefinitionID *uuid.UUID             `json:"job_definition_id" gorm:"type:uuid"`
	WorkflowID      *uuid.UUID             `json:"workflow_id" gorm:"type:uuid"`
	CronExpression  *string                `json:"cron_expression"`
	CalendarID      *uuid.UUID             `json:"calendar_id" gorm:"type:uuid"`
	TimeZone        string                 `json:"time_zone" gorm:"default:'UTC'"`
	IsActive        bool                   `json:"is_active" gorm:"default:true"`
	NextRunTime     *time.Time             `json:"next_run_time"`
	LastRunTime     *time.Time             `json:"last_run_time"`
	Parameters      map[string]interface{} `json:"parameters" gorm:"type:jsonb;default:'{}'"`
	CreatedAt       time.Time              `json:"created_at"`
	UpdatedAt       time.Time              `json:"updated_at"`
	CreatedBy       *uuid.UUID             `json:"created_by" gorm:"type:uuid"`
	
	// Associations
	JobDefinition   *JobDefinition         `json:"job_definition" gorm:"foreignKey:JobDefinitionID"`
	Workflow        *Workflow              `json:"workflow" gorm:"foreignKey:WorkflowID"`
	Calendar        *Calendar              `json:"calendar" gorm:"foreignKey:CalendarID"`
	Creator         *User                  `json:"creator" gorm:"foreignKey:CreatedBy"`
	JobInstances    []JobInstance          `json:"job_instances" gorm:"foreignKey:ScheduleID"`
}

// Calendar represents a business calendar
type Calendar struct {
	ID              uuid.UUID              `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	Name            string                 `json:"name" gorm:"not null;uniqueIndex"`
	Description     *string                `json:"description"`
	TimeZone        string                 `json:"time_zone" gorm:"default:'UTC'"`
	BusinessDays    []int                  `json:"business_days" gorm:"type:jsonb;default:'[1,2,3,4,5]'"` // 0=Sunday, 1=Monday, etc.
	Holidays        []time.Time            `json:"holidays" gorm:"type:jsonb;default:'[]'"`
	WorkingHours    map[string]interface{} `json:"working_hours" gorm:"type:jsonb;default:'{}'"`
	IsActive        bool                   `json:"is_active" gorm:"default:true"`
	CreatedAt       time.Time              `json:"created_at"`
	UpdatedAt       time.Time              `json:"updated_at"`
	CreatedBy       *uuid.UUID             `json:"created_by" gorm:"type:uuid"`
	
	// Associations
	Creator         *User                  `json:"creator" gorm:"foreignKey:CreatedBy"`
	Schedules       []Schedule             `json:"schedules" gorm:"foreignKey:CalendarID"`
}

// JobType constants
const (
	JobTypeShell        = "shell"
	JobTypePython       = "python"
	JobTypeJava         = "java"
	JobTypeMainframeJCL = "mainframe_jcl"
	JobTypeMainframeREXX = "mainframe_rexx"
	JobTypeMainframeCOBOL = "mainframe_cobol"
	JobTypeMainframeDB2 = "mainframe_db2"
	JobTypeMainframeIMS = "mainframe_ims"
	JobTypeMainframeVSAM = "mainframe_vsam"
	JobTypeMainframeCICS = "mainframe_cics"
	JobTypeMainframeMQ = "mainframe_mq"
)

// MainframeJobType constants
const (
	MainframeJobTypeJCL   = "JCL"
	MainframeJobTypeREXX  = "REXX"
	MainframeJobTypeCOBOL = "COBOL"
	MainframeJobTypeDB2   = "DB2"
	MainframeJobTypeIMS   = "IMS"
	MainframeJobTypeVSAM  = "VSAM"
	MainframeJobTypeCICS  = "CICS"
	MainframeJobTypeMQ    = "MQ"
)

// BeforeCreate hooks
func (jd *JobDefinition) BeforeCreate(tx *gorm.DB) error {
	if jd.ID == uuid.Nil {
		jd.ID = uuid.New()
	}
	return nil
}

func (rp *ResourcePool) BeforeCreate(tx *gorm.DB) error {
	if rp.ID == uuid.Nil {
		rp.ID = uuid.New()
	}
	return nil
}

func (en *ExecutionNode) BeforeCreate(tx *gorm.DB) error {
	if en.ID == uuid.Nil {
		en.ID = uuid.New()
	}
	return nil
}

func (s *Schedule) BeforeCreate(tx *gorm.DB) error {
	if s.ID == uuid.Nil {
		s.ID = uuid.New()
	}
	return nil
}

func (c *Calendar) BeforeCreate(tx *gorm.DB) error {
	if c.ID == uuid.Nil {
		c.ID = uuid.New()
	}
	return nil
}
