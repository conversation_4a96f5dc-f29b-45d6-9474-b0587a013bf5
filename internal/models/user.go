package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// User represents a system user
type User struct {
	ID          uuid.UUID  `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	Username    string     `json:"username" gorm:"uniqueIndex;not null"`
	Email       string     `json:"email" gorm:"uniqueIndex;not null"`
	PasswordHash string    `json:"-" gorm:"not null"`
	FirstName   *string    `json:"first_name"`
	LastName    *string    `json:"last_name"`
	IsActive    bool       `json:"is_active" gorm:"default:true"`
	IsAdmin     bool       `json:"is_admin" gorm:"default:false"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
	LastLoginAt *time.Time `json:"last_login_at"`

	// Associations
	Roles []Role `json:"roles" gorm:"many2many:user_roles;"`
}

// Role represents a user role with permissions
type Role struct {
	ID          uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	Name        string    `json:"name" gorm:"uniqueIndex;not null"`
	Description *string   `json:"description"`
	Permissions []string  `json:"permissions" gorm:"type:jsonb;default:'[]'"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`

	// Associations
	Users []User `json:"users" gorm:"many2many:user_roles;"`
}

// UserRole represents the many-to-many relationship between users and roles
type UserRole struct {
	UserID     uuid.UUID  `json:"user_id" gorm:"type:uuid;primaryKey"`
	RoleID     uuid.UUID  `json:"role_id" gorm:"type:uuid;primaryKey"`
	AssignedAt time.Time  `json:"assigned_at" gorm:"default:now()"`
	AssignedBy *uuid.UUID `json:"assigned_by" gorm:"type:uuid"`

	// Associations
	User       User  `json:"user" gorm:"foreignKey:UserID"`
	Role       Role  `json:"role" gorm:"foreignKey:RoleID"`
	AssignedByUser *User `json:"assigned_by_user" gorm:"foreignKey:AssignedBy"`
}

// ResourcePool represents a pool of execution resources
type ResourcePool struct {
	ID                uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	Name              string    `json:"name" gorm:"uniqueIndex;not null"`
	Description       *string   `json:"description"`
	MaxConcurrentJobs int       `json:"max_concurrent_jobs" gorm:"default:10"`
	Priority          int       `json:"priority" gorm:"default:100"`
	Tags              []string  `json:"tags" gorm:"type:jsonb;default:'[]'"`
	IsActive          bool      `json:"is_active" gorm:"default:true"`
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`

	// Associations
	ExecutionNodes []ExecutionNode `json:"execution_nodes" gorm:"foreignKey:ResourcePoolID"`
	JobDefinitions []JobDefinition `json:"job_definitions" gorm:"foreignKey:ResourcePoolID"`
}

// ExecutionNode represents a node that can execute jobs
type ExecutionNode struct {
	ID                uuid.UUID     `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	Name              string        `json:"name" gorm:"uniqueIndex;not null"`
	Hostname          string        `json:"hostname" gorm:"not null"`
	IPAddress         *string       `json:"ip_address"`
	Port              int           `json:"port" gorm:"default:8080"`
	Platform          string        `json:"platform" gorm:"not null"` // linux, windows, darwin
	Architecture      string        `json:"architecture" gorm:"not null"` // amd64, arm64
	ResourcePoolID    *uuid.UUID    `json:"resource_pool_id" gorm:"type:uuid"`
	MaxConcurrentJobs int           `json:"max_concurrent_jobs" gorm:"default:5"`
	CurrentJobs       int           `json:"current_jobs" gorm:"default:0"`
	CPUCores          *int          `json:"cpu_cores"`
	MemoryMB          *int          `json:"memory_mb"`
	DiskSpaceGB       *int          `json:"disk_space_gb"`
	Status            string        `json:"status" gorm:"default:'offline'"` // online, offline, maintenance, error
	LastHeartbeat     *time.Time    `json:"last_heartbeat"`
	Metadata          map[string]interface{} `json:"metadata" gorm:"type:jsonb;default:'{}'"`
	CreatedAt         time.Time     `json:"created_at"`
	UpdatedAt         time.Time     `json:"updated_at"`

	// Associations
	ResourcePool *ResourcePool `json:"resource_pool" gorm:"foreignKey:ResourcePoolID"`
	JobInstances []JobInstance `json:"job_instances" gorm:"foreignKey:ExecutionNodeID"`
}

// JobDefinition represents a job template
type JobDefinition struct {
	ID                   uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	Name                 string    `json:"name" gorm:"not null"`
	Description          *string   `json:"description"`
	JobType              string    `json:"job_type" gorm:"not null"` // shell, sql, http, python, docker, etc.
	Command              string    `json:"command" gorm:"not null"`
	WorkingDirectory     *string   `json:"working_directory"`
	EnvironmentVariables map[string]string `json:"environment_variables" gorm:"type:jsonb;default:'{}'"`
	Parameters           map[string]interface{} `json:"parameters" gorm:"type:jsonb;default:'{}'"`
	TimeoutSeconds       int       `json:"timeout_seconds" gorm:"default:3600"`
	RetryCount           int       `json:"retry_count" gorm:"default:0"`
	RetryDelaySeconds    int       `json:"retry_delay_seconds" gorm:"default:60"`
	ResourcePoolID       *uuid.UUID `json:"resource_pool_id" gorm:"type:uuid"`
	OwnerID              *uuid.UUID `json:"owner_id" gorm:"type:uuid"`
	IsActive             bool      `json:"is_active" gorm:"default:true"`
	Tags                 []string  `json:"tags" gorm:"type:jsonb;default:'[]'"`
	CreatedAt            time.Time `json:"created_at"`
	UpdatedAt            time.Time `json:"updated_at"`

	// Associations
	ResourcePool    *ResourcePool   `json:"resource_pool" gorm:"foreignKey:ResourcePoolID"`
	Owner           *User           `json:"owner" gorm:"foreignKey:OwnerID"`
	Schedules       []Schedule      `json:"schedules" gorm:"foreignKey:JobDefinitionID"`
	JobInstances    []JobInstance   `json:"job_instances" gorm:"foreignKey:JobDefinitionID"`
	ParentDependencies []JobDependency `json:"parent_dependencies" gorm:"foreignKey:ChildJobID"`
	ChildDependencies  []JobDependency `json:"child_dependencies" gorm:"foreignKey:ParentJobID"`
	EventTriggers   []EventTrigger  `json:"event_triggers" gorm:"foreignKey:JobDefinitionID"`
	SLADefinitions  []SLADefinition `json:"sla_definitions" gorm:"foreignKey:JobDefinitionID"`
}

// Schedule represents a job schedule
type Schedule struct {
	ID               uuid.UUID  `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	JobDefinitionID  uuid.UUID  `json:"job_definition_id" gorm:"type:uuid;not null"`
	Name             string     `json:"name" gorm:"not null"`
	CronExpression   *string    `json:"cron_expression"`
	Timezone         string     `json:"timezone" gorm:"default:'UTC'"`
	StartDate        *time.Time `json:"start_date"`
	EndDate          *time.Time `json:"end_date"`
	IsActive         bool       `json:"is_active" gorm:"default:true"`
	MaxInstances     int        `json:"max_instances" gorm:"default:1"`
	OverlapPolicy    string     `json:"overlap_policy" gorm:"default:'skip'"` // skip, queue, replace
	CalendarID       *uuid.UUID `json:"calendar_id" gorm:"type:uuid"`
	CreatedAt        time.Time  `json:"created_at"`
	UpdatedAt        time.Time  `json:"updated_at"`

	// Associations
	JobDefinition *JobDefinition `json:"job_definition" gorm:"foreignKey:JobDefinitionID"`
	Calendar      *Calendar      `json:"calendar" gorm:"foreignKey:CalendarID"`
	JobInstances  []JobInstance  `json:"job_instances" gorm:"foreignKey:ScheduleID"`
}

// Calendar represents a business calendar for scheduling
type Calendar struct {
	ID           uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	Name         string    `json:"name" gorm:"uniqueIndex;not null"`
	Description  *string   `json:"description"`
	Timezone     string    `json:"timezone" gorm:"default:'UTC'"`
	BusinessDays []int     `json:"business_days" gorm:"type:integer[];default:'{1,2,3,4,5}'"` // 1=Monday, 7=Sunday
	Holidays     []string  `json:"holidays" gorm:"type:jsonb;default:'[]'"` // Array of holiday dates
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`

	// Associations
	Schedules []Schedule `json:"schedules" gorm:"foreignKey:CalendarID"`
}

// BeforeCreate hook for User
func (u *User) BeforeCreate(tx *gorm.DB) error {
	if u.ID == uuid.Nil {
		u.ID = uuid.New()
	}
	return nil
}

// BeforeCreate hook for Role
func (r *Role) BeforeCreate(tx *gorm.DB) error {
	if r.ID == uuid.Nil {
		r.ID = uuid.New()
	}
	return nil
}

// BeforeCreate hook for ResourcePool
func (rp *ResourcePool) BeforeCreate(tx *gorm.DB) error {
	if rp.ID == uuid.Nil {
		rp.ID = uuid.New()
	}
	return nil
}

// BeforeCreate hook for ExecutionNode
func (en *ExecutionNode) BeforeCreate(tx *gorm.DB) error {
	if en.ID == uuid.Nil {
		en.ID = uuid.New()
	}
	return nil
}

// BeforeCreate hook for JobDefinition
func (jd *JobDefinition) BeforeCreate(tx *gorm.DB) error {
	if jd.ID == uuid.Nil {
		jd.ID = uuid.New()
	}
	return nil
}

// BeforeCreate hook for Schedule
func (s *Schedule) BeforeCreate(tx *gorm.DB) error {
	if s.ID == uuid.Nil {
		s.ID = uuid.New()
	}
	return nil
}

// BeforeCreate hook for Calendar
func (c *Calendar) BeforeCreate(tx *gorm.DB) error {
	if c.ID == uuid.Nil {
		c.ID = uuid.New()
	}
	return nil
}
