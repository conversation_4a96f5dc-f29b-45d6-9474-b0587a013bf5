package workflow

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/workflowmaster/workflowmaster/internal/database"
	"github.com/workflowmaster/workflowmaster/internal/models"
)

// WorkflowEngine manages workflow execution
type WorkflowEngine struct {
	db              *database.Database
	runningWorkflows map[uuid.UUID]*WorkflowExecution
	mutex           sync.RWMutex
	ctx             context.Context
	cancel          context.CancelFunc
	running         bool
}

// WorkflowExecution represents a running workflow instance
type WorkflowExecution struct {
	Instance    *models.WorkflowInstance
	Definition  *WorkflowDefinition
	Context     *ExecutionContext
	JobStates   map[string]*JobState
	Cancel      context.CancelFunc
	StartTime   time.Time
}

// WorkflowDefinition represents the structure of a workflow
type WorkflowDefinition struct {
	Version     string                 `json:"version"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Jobs        map[string]*JobNode    `json:"jobs"`
	Edges       []*Edge                `json:"edges"`
	Parameters  map[string]interface{} `json:"parameters"`
	Variables   map[string]interface{} `json:"variables"`
}

// JobNode represents a job in the workflow
type JobNode struct {
	ID           string                 `json:"id"`
	Name         string                 `json:"name"`
	Type         string                 `json:"type"`
	JobDefID     string                 `json:"job_definition_id"`
	Parameters   map[string]interface{} `json:"parameters"`
	Condition    string                 `json:"condition,omitempty"`
	RetryPolicy  *RetryPolicy           `json:"retry_policy,omitempty"`
	Timeout      int                    `json:"timeout,omitempty"`
	OnSuccess    []string               `json:"on_success,omitempty"`
	OnFailure    []string               `json:"on_failure,omitempty"`
	OnCompletion []string               `json:"on_completion,omitempty"`
}

// Edge represents a connection between jobs
type Edge struct {
	From      string `json:"from"`
	To        string `json:"to"`
	Condition string `json:"condition,omitempty"`
	Type      string `json:"type"` // success, failure, completion, always
}

// RetryPolicy defines retry behavior for a job
type RetryPolicy struct {
	MaxAttempts int    `json:"max_attempts"`
	Delay       int    `json:"delay_seconds"`
	BackoffType string `json:"backoff_type"` // fixed, exponential, linear
	MaxDelay    int    `json:"max_delay_seconds,omitempty"`
}

// ExecutionContext holds workflow execution state
type ExecutionContext struct {
	WorkflowID   uuid.UUID              `json:"workflow_id"`
	InstanceID   uuid.UUID              `json:"instance_id"`
	Parameters   map[string]interface{} `json:"parameters"`
	Variables    map[string]interface{} `json:"variables"`
	JobOutputs   map[string]interface{} `json:"job_outputs"`
	StartTime    time.Time              `json:"start_time"`
	CurrentPhase string                 `json:"current_phase"`
}

// JobState represents the state of a job in workflow execution
type JobState struct {
	ID          string                 `json:"id"`
	Status      string                 `json:"status"` // pending, running, success, failed, skipped, cancelled
	StartTime   *time.Time             `json:"start_time,omitempty"`
	EndTime     *time.Time             `json:"end_time,omitempty"`
	Attempts    int                    `json:"attempts"`
	Output      map[string]interface{} `json:"output,omitempty"`
	Error       string                 `json:"error,omitempty"`
	InstanceID  *uuid.UUID             `json:"instance_id,omitempty"`
}

// NewWorkflowEngine creates a new workflow engine
func NewWorkflowEngine(db *database.Database) *WorkflowEngine {
	ctx, cancel := context.WithCancel(context.Background())
	
	return &WorkflowEngine{
		db:               db,
		runningWorkflows: make(map[uuid.UUID]*WorkflowExecution),
		ctx:              ctx,
		cancel:           cancel,
		running:          false,
	}
}

// Start starts the workflow engine
func (we *WorkflowEngine) Start() error {
	we.mutex.Lock()
	defer we.mutex.Unlock()

	if we.running {
		return fmt.Errorf("workflow engine is already running")
	}

	log.Println("Starting workflow engine...")

	// Start workflow monitor
	go we.monitorWorkflows()

	// Resume any running workflows
	if err := we.resumeRunningWorkflows(); err != nil {
		log.Printf("Warning: failed to resume running workflows: %v", err)
	}

	we.running = true
	log.Println("Workflow engine started successfully")

	return nil
}

// Stop stops the workflow engine
func (we *WorkflowEngine) Stop() error {
	we.mutex.Lock()
	defer we.mutex.Unlock()

	if !we.running {
		return fmt.Errorf("workflow engine is not running")
	}

	log.Println("Stopping workflow engine...")

	// Cancel all running workflows
	for _, execution := range we.runningWorkflows {
		if execution.Cancel != nil {
			execution.Cancel()
		}
	}

	// Cancel context to stop goroutines
	we.cancel()

	we.running = false
	log.Println("Workflow engine stopped successfully")

	return nil
}

// ExecuteWorkflow starts execution of a workflow
func (we *WorkflowEngine) ExecuteWorkflow(workflowID uuid.UUID, parameters map[string]interface{}, createdBy *uuid.UUID) (*models.WorkflowInstance, error) {
	// Load workflow definition
	var workflow models.Workflow
	if err := we.db.DB.First(&workflow, workflowID).Error; err != nil {
		return nil, fmt.Errorf("workflow not found: %w", err)
	}

	if !workflow.IsActive {
		return nil, fmt.Errorf("workflow is not active")
	}

	// Parse workflow definition
	definition, err := we.parseWorkflowDefinition(workflow.Definition)
	if err != nil {
		return nil, fmt.Errorf("failed to parse workflow definition: %w", err)
	}

	// Create workflow instance
	instance := &models.WorkflowInstance{
		WorkflowID:  workflowID,
		Status:      models.WorkflowStatusPending,
		ScheduledAt: time.Now().UTC(),
		Parameters:  parameters,
		CreatedBy:   createdBy,
		ExecutionContext: map[string]interface{}{
			"phase": "initializing",
		},
	}

	if err := we.db.DB.Create(instance).Error; err != nil {
		return nil, fmt.Errorf("failed to create workflow instance: %w", err)
	}

	// Start workflow execution
	go we.executeWorkflowInstance(instance, definition)

	log.Printf("Started workflow execution: %s (Instance: %s)", workflow.Name, instance.ID)
	return instance, nil
}

// executeWorkflowInstance executes a workflow instance
func (we *WorkflowEngine) executeWorkflowInstance(instance *models.WorkflowInstance, definition *WorkflowDefinition) {
	ctx, cancel := context.WithCancel(we.ctx)
	defer cancel()

	// Create execution context
	execCtx := &ExecutionContext{
		WorkflowID:   instance.WorkflowID,
		InstanceID:   instance.ID,
		Parameters:   instance.Parameters,
		Variables:    make(map[string]interface{}),
		JobOutputs:   make(map[string]interface{}),
		StartTime:    time.Now().UTC(),
		CurrentPhase: "starting",
	}

	// Initialize job states
	jobStates := make(map[string]*JobState)
	for jobID := range definition.Jobs {
		jobStates[jobID] = &JobState{
			ID:       jobID,
			Status:   "pending",
			Attempts: 0,
		}
	}

	// Create workflow execution
	execution := &WorkflowExecution{
		Instance:   instance,
		Definition: definition,
		Context:    execCtx,
		JobStates:  jobStates,
		Cancel:     cancel,
		StartTime:  time.Now().UTC(),
	}

	// Add to running workflows
	we.mutex.Lock()
	we.runningWorkflows[instance.ID] = execution
	we.mutex.Unlock()

	// Update instance status
	instance.Status = models.WorkflowStatusRunning
	startTime := time.Now().UTC()
	instance.StartedAt = &startTime
	we.db.DB.Save(instance)

	// Execute workflow
	err := we.runWorkflow(ctx, execution)

	// Remove from running workflows
	we.mutex.Lock()
	delete(we.runningWorkflows, instance.ID)
	we.mutex.Unlock()

	// Update final status
	completedAt := time.Now().UTC()
	instance.CompletedAt = &completedAt
	instance.ExecutionContext = map[string]interface{}{
		"job_states": jobStates,
		"variables":  execCtx.Variables,
		"outputs":    execCtx.JobOutputs,
	}

	if err != nil {
		instance.Status = models.WorkflowStatusFailed
		log.Printf("Workflow execution failed: %s (Instance: %s) - %v", 
			definition.Name, instance.ID, err)
	} else {
		instance.Status = models.WorkflowStatusSuccess
		log.Printf("Workflow execution completed: %s (Instance: %s)", 
			definition.Name, instance.ID)
	}

	we.db.DB.Save(instance)
}

// runWorkflow executes the workflow logic
func (we *WorkflowEngine) runWorkflow(ctx context.Context, execution *WorkflowExecution) error {
	// Find starting jobs (jobs with no dependencies)
	startingJobs := we.findStartingJobs(execution.Definition)
	
	if len(startingJobs) == 0 {
		return fmt.Errorf("no starting jobs found in workflow")
	}

	// Execute starting jobs
	for _, jobID := range startingJobs {
		if err := we.executeJob(ctx, execution, jobID); err != nil {
			return fmt.Errorf("failed to execute starting job %s: %w", jobID, err)
		}
	}

	// Wait for workflow completion
	return we.waitForCompletion(ctx, execution)
}

// findStartingJobs finds jobs that have no incoming dependencies
func (we *WorkflowEngine) findStartingJobs(definition *WorkflowDefinition) []string {
	hasIncoming := make(map[string]bool)
	
	// Mark jobs that have incoming edges
	for _, edge := range definition.Edges {
		hasIncoming[edge.To] = true
	}

	// Find jobs without incoming edges
	var startingJobs []string
	for jobID := range definition.Jobs {
		if !hasIncoming[jobID] {
			startingJobs = append(startingJobs, jobID)
		}
	}

	return startingJobs
}

// executeJob executes a single job in the workflow
func (we *WorkflowEngine) executeJob(ctx context.Context, execution *WorkflowExecution, jobID string) error {
	jobNode := execution.Definition.Jobs[jobID]
	jobState := execution.JobStates[jobID]

	// Check if job should be executed based on conditions
	if jobNode.Condition != "" {
		shouldExecute, err := we.evaluateCondition(jobNode.Condition, execution.Context)
		if err != nil {
			return fmt.Errorf("failed to evaluate condition for job %s: %w", jobID, err)
		}
		if !shouldExecute {
			jobState.Status = "skipped"
			log.Printf("Job %s skipped due to condition", jobID)
			return we.triggerNextJobs(ctx, execution, jobID, "skipped")
		}
	}

	// Update job state
	jobState.Status = "running"
	startTime := time.Now().UTC()
	jobState.StartTime = &startTime

	log.Printf("Executing workflow job: %s", jobID)

	// Execute the job based on type
	var err error
	switch jobNode.Type {
	case "job":
		err = we.executeJobDefinition(ctx, execution, jobID)
	case "parallel":
		err = we.executeParallelGroup(ctx, execution, jobID)
	case "condition":
		err = we.executeConditionalBranch(ctx, execution, jobID)
	default:
		err = fmt.Errorf("unsupported job type: %s", jobNode.Type)
	}

	// Update job completion state
	endTime := time.Now().UTC()
	jobState.EndTime = &endTime

	if err != nil {
		jobState.Status = "failed"
		jobState.Error = err.Error()
		
		// Handle retry logic
		if jobNode.RetryPolicy != nil && jobState.Attempts < jobNode.RetryPolicy.MaxAttempts {
			return we.retryJob(ctx, execution, jobID)
		}
		
		log.Printf("Workflow job failed: %s - %v", jobID, err)
		return we.triggerNextJobs(ctx, execution, jobID, "failed")
	}

	jobState.Status = "success"
	log.Printf("Workflow job completed: %s", jobID)
	return we.triggerNextJobs(ctx, execution, jobID, "success")
}

// parseWorkflowDefinition parses workflow definition from JSON
func (we *WorkflowEngine) parseWorkflowDefinition(definition map[string]interface{}) (*WorkflowDefinition, error) {
	jsonData, err := json.Marshal(definition)
	if err != nil {
		return nil, err
	}

	var workflowDef WorkflowDefinition
	if err := json.Unmarshal(jsonData, &workflowDef); err != nil {
		return nil, err
	}

	return &workflowDef, nil
}

// executeJobDefinition executes a regular job definition
func (we *WorkflowEngine) executeJobDefinition(ctx context.Context, execution *WorkflowExecution, jobID string) error {
	jobNode := execution.Definition.Jobs[jobID]

	// Parse job definition ID
	jobDefID, err := uuid.Parse(jobNode.JobDefID)
	if err != nil {
		return fmt.Errorf("invalid job definition ID: %w", err)
	}

	// Load job definition
	var jobDef models.JobDefinition
	if err := we.db.DB.First(&jobDef, jobDefID).Error; err != nil {
		return fmt.Errorf("job definition not found: %w", err)
	}

	// Merge parameters
	parameters := make(map[string]interface{})
	for k, v := range jobDef.Parameters {
		parameters[k] = v
	}
	for k, v := range jobNode.Parameters {
		parameters[k] = v
	}

	// Create job instance
	jobInstance := &models.JobInstance{
		JobDefinitionID:      &jobDefID,
		WorkflowInstanceID:   &execution.Instance.ID,
		Status:               models.JobStatusPending,
		Priority:             100,
		ScheduledAt:          time.Now().UTC(),
		Parameters:           parameters,
		EnvironmentVariables: jobDef.EnvironmentVariables,
	}

	if err := we.db.DB.Create(jobInstance).Error; err != nil {
		return fmt.Errorf("failed to create job instance: %w", err)
	}

	// Store instance ID in job state
	execution.JobStates[jobID].InstanceID = &jobInstance.ID

	// Wait for job completion
	return we.waitForJobCompletion(ctx, jobInstance.ID)
}

// waitForJobCompletion waits for a job instance to complete
func (we *WorkflowEngine) waitForJobCompletion(ctx context.Context, instanceID uuid.UUID) error {
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-ticker.C:
			var instance models.JobInstance
			if err := we.db.DB.First(&instance, instanceID).Error; err != nil {
				return fmt.Errorf("failed to load job instance: %w", err)
			}

			switch instance.Status {
			case models.JobStatusSuccess:
				return nil
			case models.JobStatusFailed, models.JobStatusCancelled, models.JobStatusTimeout:
				return fmt.Errorf("job failed with status: %s", instance.Status)
			}
		}
	}
}

// triggerNextJobs triggers the next jobs based on the current job's completion status
func (we *WorkflowEngine) triggerNextJobs(ctx context.Context, execution *WorkflowExecution, jobID, status string) error {
	// Find outgoing edges from this job
	var nextJobs []string
	for _, edge := range execution.Definition.Edges {
		if edge.From == jobID {
			// Check if edge condition matches the job status
			if we.edgeConditionMatches(edge, status) {
				nextJobs = append(nextJobs, edge.To)
			}
		}
	}

	// Execute next jobs
	for _, nextJobID := range nextJobs {
		// Check if all dependencies for the next job are satisfied
		if we.areJobDependenciesSatisfied(execution, nextJobID) {
			if err := we.executeJob(ctx, execution, nextJobID); err != nil {
				return fmt.Errorf("failed to execute next job %s: %w", nextJobID, err)
			}
		}
	}

	return nil
}

// edgeConditionMatches checks if an edge condition matches the job status
func (we *WorkflowEngine) edgeConditionMatches(edge *Edge, status string) bool {
	switch edge.Type {
	case "success":
		return status == "success"
	case "failure":
		return status == "failed"
	case "completion":
		return status == "success" || status == "failed"
	case "always":
		return true
	default:
		return status == "success" // Default to success
	}
}

// areJobDependenciesSatisfied checks if all dependencies for a job are satisfied
func (we *WorkflowEngine) areJobDependenciesSatisfied(execution *WorkflowExecution, jobID string) bool {
	// Find all incoming edges to this job
	for _, edge := range execution.Definition.Edges {
		if edge.To == jobID {
			fromJobState := execution.JobStates[edge.From]

			// Check if the dependency is satisfied
			if !we.isDependencySatisfied(edge, fromJobState) {
				return false
			}
		}
	}
	return true
}

// isDependencySatisfied checks if a specific dependency is satisfied
func (we *WorkflowEngine) isDependencySatisfied(edge *Edge, fromJobState *JobState) bool {
	switch edge.Type {
	case "success":
		return fromJobState.Status == "success"
	case "failure":
		return fromJobState.Status == "failed"
	case "completion":
		return fromJobState.Status == "success" || fromJobState.Status == "failed"
	case "always":
		return fromJobState.Status != "pending" && fromJobState.Status != "running"
	default:
		return fromJobState.Status == "success"
	}
}

// waitForCompletion waits for the entire workflow to complete
func (we *WorkflowEngine) waitForCompletion(ctx context.Context, execution *WorkflowExecution) error {
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-ticker.C:
			if we.isWorkflowComplete(execution) {
				return nil
			}
		}
	}
}

// isWorkflowComplete checks if all jobs in the workflow are complete
func (we *WorkflowEngine) isWorkflowComplete(execution *WorkflowExecution) bool {
	for _, jobState := range execution.JobStates {
		if jobState.Status == "pending" || jobState.Status == "running" {
			return false
		}
	}
	return true
}

// evaluateCondition evaluates a condition expression
func (we *WorkflowEngine) evaluateCondition(condition string, ctx *ExecutionContext) (bool, error) {
	// TODO: Implement proper condition evaluation
	// For now, return true for all conditions
	return true, nil
}

// executeParallelGroup executes a group of jobs in parallel
func (we *WorkflowEngine) executeParallelGroup(ctx context.Context, execution *WorkflowExecution, jobID string) error {
	// TODO: Implement parallel job execution
	return fmt.Errorf("parallel job execution not implemented yet")
}

// executeConditionalBranch executes conditional branching logic
func (we *WorkflowEngine) executeConditionalBranch(ctx context.Context, execution *WorkflowExecution, jobID string) error {
	// TODO: Implement conditional branching
	return fmt.Errorf("conditional branching not implemented yet")
}

// retryJob implements retry logic for failed jobs
func (we *WorkflowEngine) retryJob(ctx context.Context, execution *WorkflowExecution, jobID string) error {
	jobNode := execution.Definition.Jobs[jobID]
	jobState := execution.JobStates[jobID]

	if jobNode.RetryPolicy == nil {
		return fmt.Errorf("no retry policy defined")
	}

	jobState.Attempts++

	// Calculate delay
	delay := time.Duration(jobNode.RetryPolicy.Delay) * time.Second
	switch jobNode.RetryPolicy.BackoffType {
	case "exponential":
		delay = delay * time.Duration(1<<uint(jobState.Attempts-1))
	case "linear":
		delay = delay * time.Duration(jobState.Attempts)
	}

	// Apply max delay if specified
	if jobNode.RetryPolicy.MaxDelay > 0 {
		maxDelay := time.Duration(jobNode.RetryPolicy.MaxDelay) * time.Second
		if delay > maxDelay {
			delay = maxDelay
		}
	}

	log.Printf("Retrying job %s in %v (attempt %d/%d)",
		jobID, delay, jobState.Attempts, jobNode.RetryPolicy.MaxAttempts)

	// Wait for delay
	select {
	case <-ctx.Done():
		return ctx.Err()
	case <-time.After(delay):
	}

	// Reset job state for retry
	jobState.Status = "pending"
	jobState.Error = ""

	// Execute job again
	return we.executeJob(ctx, execution, jobID)
}

// monitorWorkflows monitors running workflows
func (we *WorkflowEngine) monitorWorkflows() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-we.ctx.Done():
			return
		case <-ticker.C:
			we.checkWorkflowTimeouts()
		}
	}
}

// checkWorkflowTimeouts checks for workflow timeouts
func (we *WorkflowEngine) checkWorkflowTimeouts() {
	we.mutex.RLock()
	defer we.mutex.RUnlock()

	for _, execution := range we.runningWorkflows {
		// TODO: Implement workflow timeout logic
		// Check if workflow has been running too long
	}
}

// resumeRunningWorkflows resumes workflows that were running when the engine stopped
func (we *WorkflowEngine) resumeRunningWorkflows() error {
	var runningInstances []models.WorkflowInstance
	err := we.db.DB.Where("status = ?", models.WorkflowStatusRunning).
		Preload("Workflow").
		Find(&runningInstances).Error
	if err != nil {
		return err
	}

	for _, instance := range runningInstances {
		log.Printf("Resuming workflow instance: %s", instance.ID)
		// TODO: Implement workflow resumption logic
	}

	return nil
}

// GetRunningWorkflows returns all currently running workflows
func (we *WorkflowEngine) GetRunningWorkflows() map[uuid.UUID]*WorkflowExecution {
	we.mutex.RLock()
	defer we.mutex.RUnlock()

	// Create a copy to avoid race conditions
	workflows := make(map[uuid.UUID]*WorkflowExecution)
	for id, execution := range we.runningWorkflows {
		workflows[id] = execution
	}
	return workflows
}

// CancelWorkflow cancels a running workflow
func (we *WorkflowEngine) CancelWorkflow(instanceID uuid.UUID) error {
	we.mutex.RLock()
	execution, exists := we.runningWorkflows[instanceID]
	we.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("workflow instance not found or not running: %s", instanceID)
	}

	if execution.Cancel != nil {
		execution.Cancel()
	}

	// Update instance status
	execution.Instance.Status = models.WorkflowStatusCancelled
	completedAt := time.Now().UTC()
	execution.Instance.CompletedAt = &completedAt
	we.db.DB.Save(execution.Instance)

	log.Printf("Cancelled workflow instance: %s", instanceID)
	return nil
}
