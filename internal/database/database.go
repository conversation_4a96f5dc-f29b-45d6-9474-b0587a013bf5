package database

import (
	"fmt"
	"log"
	"time"

	"github.com/workflowmaster/workflowmaster/internal/models"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// Config holds database configuration
type Config struct {
	Host     string
	Port     int
	User     string
	Password string
	DBName   string
	SSLMode  string
	Timezone string
}

// Database wraps the GORM database connection
type Database struct {
	DB *gorm.DB
}

// NewDatabase creates a new database connection
func NewDatabase(config Config) (*Database, error) {
	dsn := fmt.Sprintf(
		"host=%s port=%d user=%s password=%s dbname=%s sslmode=%s TimeZone=%s",
		config.Host,
		config.Port,
		config.User,
		config.Password,
		config.DBName,
		config.SSLMode,
		config.Timezone,
	)

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
		NowFunc: func() time.Time {
			return time.Now().UTC()
		},
	})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// Configure connection pool
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	// Set connection pool settings
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxLifetime(time.Hour)

	return &Database{DB: db}, nil
}

// AutoMigrate runs database migrations
func (d *Database) AutoMigrate() error {
	log.Println("Running database migrations...")

	err := d.DB.AutoMigrate(
		// User and authentication models
		&models.User{},
		&models.Role{},
		&models.UserRole{},

		// Resource and execution models
		&models.ResourcePool{},
		&models.ExecutionNode{},

		// Job and workflow models
		&models.JobDefinition{},
		&models.Schedule{},
		&models.Calendar{},
		&models.JobInstance{},
		&models.Workflow{},
		&models.WorkflowInstance{},
		&models.JobDependency{},

		// Event and trigger models
		&models.Event{},
		&models.EventTrigger{},

		// SLA and monitoring models
		&models.SLADefinition{},
		&models.AuditLog{},
		&models.Notification{},
	)

	if err != nil {
		return fmt.Errorf("failed to run migrations: %w", err)
	}

	log.Println("Database migrations completed successfully")
	return nil
}

// Close closes the database connection
func (d *Database) Close() error {
	sqlDB, err := d.DB.DB()
	if err != nil {
		return err
	}
	return sqlDB.Close()
}

// Health checks database connectivity
func (d *Database) Health() error {
	sqlDB, err := d.DB.DB()
	if err != nil {
		return err
	}
	return sqlDB.Ping()
}

// SeedData creates initial data for development/testing
func (d *Database) SeedData() error {
	log.Println("Seeding initial data...")

	// Create default admin role
	adminRole := &models.Role{
		Name:        "admin",
		Description: stringPtr("System Administrator"),
		Permissions: []string{
			"users:read", "users:write", "users:delete",
			"jobs:read", "jobs:write", "jobs:delete", "jobs:execute",
			"workflows:read", "workflows:write", "workflows:delete", "workflows:execute",
			"schedules:read", "schedules:write", "schedules:delete",
			"resources:read", "resources:write", "resources:delete",
			"system:admin",
		},
	}

	// Create default user role
	userRole := &models.Role{
		Name:        "user",
		Description: stringPtr("Regular User"),
		Permissions: []string{
			"jobs:read", "jobs:write", "jobs:execute",
			"workflows:read", "workflows:write", "workflows:execute",
			"schedules:read", "schedules:write",
		},
	}

	// Create viewer role
	viewerRole := &models.Role{
		Name:        "viewer",
		Description: stringPtr("Read-only User"),
		Permissions: []string{
			"jobs:read",
			"workflows:read",
			"schedules:read",
		},
	}

	// Insert roles if they don't exist
	for _, role := range []*models.Role{adminRole, userRole, viewerRole} {
		var existingRole models.Role
		result := d.DB.Where("name = ?", role.Name).First(&existingRole)
		if result.Error == gorm.ErrRecordNotFound {
			if err := d.DB.Create(role).Error; err != nil {
				return fmt.Errorf("failed to create role %s: %w", role.Name, err)
			}
			log.Printf("Created role: %s", role.Name)
		}
	}

	// Create default resource pool
	defaultPool := &models.ResourcePool{
		Name:              "default",
		Description:       stringPtr("Default resource pool"),
		MaxConcurrentJobs: 50,
		Priority:          100,
		Tags:              []string{"default"},
		IsActive:          true,
	}

	var existingPool models.ResourcePool
	result := d.DB.Where("name = ?", defaultPool.Name).First(&existingPool)
	if result.Error == gorm.ErrRecordNotFound {
		if err := d.DB.Create(defaultPool).Error; err != nil {
			return fmt.Errorf("failed to create default resource pool: %w", err)
		}
		log.Println("Created default resource pool")
	}

	// Create default calendar
	defaultCalendar := &models.Calendar{
		Name:         "business_days",
		Description:  stringPtr("Standard business days calendar"),
		Timezone:     "UTC",
		BusinessDays: []int{1, 2, 3, 4, 5}, // Monday to Friday
		Holidays:     []string{},
	}

	var existingCalendar models.Calendar
	result = d.DB.Where("name = ?", defaultCalendar.Name).First(&existingCalendar)
	if result.Error == gorm.ErrRecordNotFound {
		if err := d.DB.Create(defaultCalendar).Error; err != nil {
			return fmt.Errorf("failed to create default calendar: %w", err)
		}
		log.Println("Created default calendar")
	}

	log.Println("Data seeding completed successfully")
	return nil
}

// CreateDefaultAdmin creates a default admin user for initial setup
func (d *Database) CreateDefaultAdmin(username, email, password string) error {
	// Check if admin user already exists
	var existingUser models.User
	result := d.DB.Where("username = ? OR email = ?", username, email).First(&existingUser)
	if result.Error != gorm.ErrRecordNotFound {
		return fmt.Errorf("user with username %s or email %s already exists", username, email)
	}

	// Get admin role
	var adminRole models.Role
	if err := d.DB.Where("name = ?", "admin").First(&adminRole).Error; err != nil {
		return fmt.Errorf("admin role not found: %w", err)
	}

	// Create admin user
	adminUser := &models.User{
		Username:     username,
		Email:        email,
		PasswordHash: password, // This should be hashed before calling this function
		FirstName:    stringPtr("System"),
		LastName:     stringPtr("Administrator"),
		IsActive:     true,
		IsAdmin:      true,
	}

	if err := d.DB.Create(adminUser).Error; err != nil {
		return fmt.Errorf("failed to create admin user: %w", err)
	}

	// Assign admin role
	userRole := &models.UserRole{
		UserID: adminUser.ID,
		RoleID: adminRole.ID,
	}

	if err := d.DB.Create(userRole).Error; err != nil {
		return fmt.Errorf("failed to assign admin role: %w", err)
	}

	log.Printf("Created default admin user: %s", username)
	return nil
}

// Helper function to create string pointers
func stringPtr(s string) *string {
	return &s
}

// Transaction executes a function within a database transaction
func (d *Database) Transaction(fn func(*gorm.DB) error) error {
	return d.DB.Transaction(fn)
}

// GetDB returns the underlying GORM database instance
func (d *Database) GetDB() *gorm.DB {
	return d.DB
}
