package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/workflowmaster/workflowmaster/internal/database"
	"github.com/workflowmaster/workflowmaster/internal/mainframe"
)

// MainframeHandler handles mainframe integration API requests
type MainframeHandler struct {
	db      *database.Database
	adapter *mainframe.MainframeAdapter
}

// NewMainframeHandler creates a new mainframe handler
func NewMainframeHandler(db *database.Database, adapter *mainframe.MainframeAdapter) *MainframeHandler {
	return &MainframeHandler{
		db:      db,
		adapter: adapter,
	}
}

// Connection Management APIs

// CreateConnection creates a new mainframe connection
// @Summary Create mainframe connection
// @Description Create a new mainframe connection profile
// @Tags mainframe
// @Accept json
// @Produce json
// @Param connection body mainframe.MainframeConnection true "Connection details"
// @Success 201 {object} mainframe.MainframeConnection
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/mainframe/connections [post]
func (h *MainframeHandler) CreateConnection(c *gin.Context) {
	var connection mainframe.MainframeConnection
	if err := c.ShouldBindJSON(&connection); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// Set owner from authenticated user
	userID := getUserIDFromContext(c)
	if userID != nil {
		connection.CreatedBy = userID
	}

	// Create connection
	if err := h.adapter.CreateConnection(&connection); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusCreated, connection)
}

// GetConnections retrieves mainframe connections
// @Summary Get mainframe connections
// @Description Retrieve a list of mainframe connections with pagination
// @Tags mainframe
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param environment query string false "Filter by environment"
// @Param system_type query string false "Filter by system type"
// @Success 200 {object} PaginatedResponse{data=[]mainframe.MainframeConnection}
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/mainframe/connections [get]
func (h *MainframeHandler) GetConnections(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	environment := c.Query("environment")
	systemType := c.Query("system_type")

	offset := (page - 1) * limit

	query := h.db.DB.Model(&mainframe.MainframeConnection{})

	// Apply filters
	if environment != "" {
		query = query.Where("environment = ?", environment)
	}
	if systemType != "" {
		query = query.Where("system_type = ?", systemType)
	}

	// Get total count
	var total int64
	query.Count(&total)

	// Get paginated results
	var connections []mainframe.MainframeConnection
	if err := query.Offset(offset).Limit(limit).Find(&connections).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "failed to retrieve connections"})
		return
	}

	// Remove sensitive data
	for i := range connections {
		connections[i].PasswordEncrypted = ""
	}

	response := PaginatedResponse{
		Data:       connections,
		Page:       page,
		Limit:      limit,
		Total:      total,
		TotalPages: (total + int64(limit) - 1) / int64(limit),
	}

	c.JSON(http.StatusOK, response)
}

// GetConnection retrieves a specific mainframe connection
// @Summary Get mainframe connection by ID
// @Description Retrieve a specific mainframe connection by its ID
// @Tags mainframe
// @Produce json
// @Param id path string true "Connection ID"
// @Success 200 {object} mainframe.MainframeConnection
// @Failure 404 {object} ErrorResponse
// @Router /api/v1/mainframe/connections/{id} [get]
func (h *MainframeHandler) GetConnection(c *gin.Context) {
	id := c.Param("id")
	connectionID, err := uuid.Parse(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "invalid connection ID"})
		return
	}

	var connection mainframe.MainframeConnection
	if err := h.db.DB.First(&connection, connectionID).Error; err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{Error: "connection not found"})
		return
	}

	// Remove sensitive data
	connection.PasswordEncrypted = ""

	c.JSON(http.StatusOK, connection)
}

// TestConnection tests a mainframe connection
// @Summary Test mainframe connection
// @Description Test connectivity to a mainframe system
// @Tags mainframe
// @Param id path string true "Connection ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/mainframe/connections/{id}/test [post]
func (h *MainframeHandler) TestConnection(c *gin.Context) {
	id := c.Param("id")
	connectionID, err := uuid.Parse(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "invalid connection ID"})
		return
	}

	// Test connection
	err = h.adapter.TestConnection(connectionID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "connection test successful",
	})
}

// Job Management APIs

// SubmitJob submits a job to mainframe
// @Summary Submit mainframe job
// @Description Submit a job to a mainframe system
// @Tags mainframe
// @Accept json
// @Produce json
// @Param submission body mainframe.MainframeJobSubmission true "Job submission"
// @Success 201 {object} mainframe.MainframeJobSubmission
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/mainframe/jobs [post]
func (h *MainframeHandler) SubmitJob(c *gin.Context) {
	var submission mainframe.MainframeJobSubmission
	if err := c.ShouldBindJSON(&submission); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// Submit job
	if err := h.adapter.SubmitJob(&submission); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusCreated, submission)
}

// GetJobSubmissions retrieves job submissions
// @Summary Get job submissions
// @Description Retrieve a list of mainframe job submissions
// @Tags mainframe
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param status query string false "Filter by status"
// @Param connection_id query string false "Filter by connection ID"
// @Success 200 {object} PaginatedResponse{data=[]mainframe.MainframeJobSubmission}
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/mainframe/jobs [get]
func (h *MainframeHandler) GetJobSubmissions(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	status := c.Query("status")
	connectionIDStr := c.Query("connection_id")

	offset := (page - 1) * limit

	query := h.db.DB.Model(&mainframe.MainframeJobSubmission{}).
		Preload("Connection").
		Order("created_at DESC")

	// Apply filters
	if status != "" {
		query = query.Where("status = ?", status)
	}
	if connectionIDStr != "" {
		connectionID, err := uuid.Parse(connectionIDStr)
		if err == nil {
			query = query.Where("connection_id = ?", connectionID)
		}
	}

	// Get total count
	var total int64
	query.Count(&total)

	// Get paginated results
	var submissions []mainframe.MainframeJobSubmission
	if err := query.Offset(offset).Limit(limit).Find(&submissions).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "failed to retrieve job submissions"})
		return
	}

	response := PaginatedResponse{
		Data:       submissions,
		Page:       page,
		Limit:      limit,
		Total:      total,
		TotalPages: (total + int64(limit) - 1) / int64(limit),
	}

	c.JSON(http.StatusOK, response)
}

// GetJobStatus retrieves job status
// @Summary Get job status
// @Description Retrieve the status of a mainframe job
// @Tags mainframe
// @Produce json
// @Param id path string true "Job submission ID"
// @Success 200 {object} mainframe.MainframeJobSubmission
// @Failure 404 {object} ErrorResponse
// @Router /api/v1/mainframe/jobs/{id} [get]
func (h *MainframeHandler) GetJobStatus(c *gin.Context) {
	id := c.Param("id")
	submissionID, err := uuid.Parse(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "invalid submission ID"})
		return
	}

	submission, err := h.adapter.GetJobStatus(submissionID)
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, submission)
}

// Data Transfer APIs

// TransferData initiates a data transfer
// @Summary Transfer data
// @Description Initiate a data transfer to/from mainframe
// @Tags mainframe
// @Accept json
// @Produce json
// @Param transfer body mainframe.MainframeDataTransfer true "Transfer details"
// @Success 201 {object} mainframe.MainframeDataTransfer
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/mainframe/transfers [post]
func (h *MainframeHandler) TransferData(c *gin.Context) {
	var transfer mainframe.MainframeDataTransfer
	if err := c.ShouldBindJSON(&transfer); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// Initiate transfer
	if err := h.adapter.TransferData(&transfer); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusCreated, transfer)
}

// GetDataTransfers retrieves data transfers
// @Summary Get data transfers
// @Description Retrieve a list of data transfers
// @Tags mainframe
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param status query string false "Filter by status"
// @Success 200 {object} PaginatedResponse{data=[]mainframe.MainframeDataTransfer}
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/mainframe/transfers [get]
func (h *MainframeHandler) GetDataTransfers(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	status := c.Query("status")

	offset := (page - 1) * limit

	query := h.db.DB.Model(&mainframe.MainframeDataTransfer{}).
		Preload("Connection").
		Order("created_at DESC")

	// Apply filters
	if status != "" {
		query = query.Where("status = ?", status)
	}

	// Get total count
	var total int64
	query.Count(&total)

	// Get paginated results
	var transfers []mainframe.MainframeDataTransfer
	if err := query.Offset(offset).Limit(limit).Find(&transfers).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "failed to retrieve transfers"})
		return
	}

	response := PaginatedResponse{
		Data:       transfers,
		Page:       page,
		Limit:      limit,
		Total:      total,
		TotalPages: (total + int64(limit) - 1) / int64(limit),
	}

	c.JSON(http.StatusOK, response)
}

// Dataset Operations APIs

// ExecuteDatasetOperation executes a dataset operation
// @Summary Execute dataset operation
// @Description Execute a dataset operation (allocate, delete, copy, etc.)
// @Tags mainframe
// @Accept json
// @Produce json
// @Param operation body mainframe.MainframeDatasetOperation true "Dataset operation"
// @Success 201 {object} mainframe.MainframeDatasetOperation
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/mainframe/datasets/operations [post]
func (h *MainframeHandler) ExecuteDatasetOperation(c *gin.Context) {
	var operation mainframe.MainframeDatasetOperation
	if err := c.ShouldBindJSON(&operation); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// Execute operation
	if err := h.adapter.ExecuteDatasetOperation(&operation); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusCreated, operation)
}

// Monitoring APIs

// GetMonitoringMetrics retrieves monitoring metrics
// @Summary Get monitoring metrics
// @Description Retrieve monitoring metrics from mainframe systems
// @Tags mainframe
// @Produce json
// @Param connection_id query string true "Connection ID"
// @Param metric_types query []string false "Metric types to retrieve"
// @Param start_time query string false "Start time (RFC3339)"
// @Param end_time query string false "End time (RFC3339)"
// @Success 200 {object} []mainframe.MainframeMonitoringMetric
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/mainframe/monitoring/metrics [get]
func (h *MainframeHandler) GetMonitoringMetrics(c *gin.Context) {
	connectionIDStr := c.Query("connection_id")
	if connectionIDStr == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "connection_id is required"})
		return
	}

	connectionID, err := uuid.Parse(connectionIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "invalid connection ID"})
		return
	}

	metricTypes := c.QueryArray("metric_types")
	if len(metricTypes) == 0 {
		metricTypes = []string{"system_resources", "job_queue"}
	}

	// Get metrics
	metrics, err := h.adapter.GetMonitoringMetrics(connectionID, metricTypes)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, metrics)
}

// System Status APIs

// GetSystemStatus retrieves mainframe adapter status
// @Summary Get system status
// @Description Retrieve the status of the mainframe adapter
// @Tags mainframe
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/mainframe/status [get]
func (h *MainframeHandler) GetSystemStatus(c *gin.Context) {
	stats := h.adapter.GetStats()
	c.JSON(http.StatusOK, stats)
}

// Configuration APIs

// GetSupportedProtocols retrieves supported protocols
// @Summary Get supported protocols
// @Description Retrieve a list of supported mainframe protocols
// @Tags mainframe
// @Produce json
// @Success 200 {object} []string
// @Router /api/v1/mainframe/protocols [get]
func (h *MainframeHandler) GetSupportedProtocols(c *gin.Context) {
	protocols := []string{"ftp", "sftp", "telnet", "tcp", "mq"}
	c.JSON(http.StatusOK, protocols)
}

// GetSupportedSystemTypes retrieves supported system types
// @Summary Get supported system types
// @Description Retrieve a list of supported mainframe system types
// @Tags mainframe
// @Produce json
// @Success 200 {object} []string
// @Router /api/v1/mainframe/system-types [get]
func (h *MainframeHandler) GetSupportedSystemTypes(c *gin.Context) {
	systemTypes := []string{"zos", "mvs", "vse", "vm"}
	c.JSON(http.StatusOK, systemTypes)
}

// GetSupportedSecuritySystems retrieves supported security systems
// @Summary Get supported security systems
// @Description Retrieve a list of supported mainframe security systems
// @Tags mainframe
// @Produce json
// @Success 200 {object} []string
// @Router /api/v1/mainframe/security-systems [get]
func (h *MainframeHandler) GetSupportedSecuritySystems(c *gin.Context) {
	securitySystems := []string{"RACF", "ACF2", "TopSecret"}
	c.JSON(http.StatusOK, securitySystems)
}
