package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/workflowmaster/workflowmaster/internal/database"
	"github.com/workflowmaster/workflowmaster/internal/models"
	"github.com/workflowmaster/workflowmaster/internal/scheduler"
)

// JobHandler handles job-related API requests
type JobHandler struct {
	db        *database.Database
	scheduler *scheduler.Scheduler
}

// NewJobHandler creates a new job handler
func NewJobHandler(db *database.Database, scheduler *scheduler.Scheduler) *JobHandler {
	return &JobHandler{
		db:        db,
		scheduler: scheduler,
	}
}

// CreateJobDefinition creates a new job definition
// @Summary Create a new job definition
// @Description Create a new job definition with the provided details
// @Tags jobs
// @Accept json
// @Produce json
// @Param job body models.JobDefinition true "Job definition"
// @Success 201 {object} models.JobDefinition
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/jobs [post]
func (h *JobHandler) CreateJobDefinition(c *gin.Context) {
	var jobDef models.JobDefinition
	if err := c.ShouldBindJSON(&jobDef); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// Set owner from authenticated user
	userID := getUserIDFromContext(c)
	if userID != nil {
		jobDef.OwnerID = userID
	}

	// Validate required fields
	if jobDef.Name == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "job name is required"})
		return
	}
	if jobDef.JobType == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "job type is required"})
		return
	}
	if jobDef.Command == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "job command is required"})
		return
	}

	// Create job definition
	if err := h.db.DB.Create(&jobDef).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "failed to create job definition"})
		return
	}

	c.JSON(http.StatusCreated, jobDef)
}

// GetJobDefinitions retrieves job definitions with pagination
// @Summary Get job definitions
// @Description Retrieve a list of job definitions with optional filtering and pagination
// @Tags jobs
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param type query string false "Filter by job type"
// @Param active query bool false "Filter by active status"
// @Success 200 {object} PaginatedResponse{data=[]models.JobDefinition}
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/jobs [get]
func (h *JobHandler) GetJobDefinitions(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	jobType := c.Query("type")
	activeStr := c.Query("active")

	offset := (page - 1) * limit

	query := h.db.DB.Model(&models.JobDefinition{}).
		Preload("Owner").
		Preload("ResourcePool")

	// Apply filters
	if jobType != "" {
		query = query.Where("job_type = ?", jobType)
	}
	if activeStr != "" {
		active, _ := strconv.ParseBool(activeStr)
		query = query.Where("is_active = ?", active)
	}

	// Get total count
	var total int64
	query.Count(&total)

	// Get paginated results
	var jobDefs []models.JobDefinition
	if err := query.Offset(offset).Limit(limit).Find(&jobDefs).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "failed to retrieve job definitions"})
		return
	}

	response := PaginatedResponse{
		Data:       jobDefs,
		Page:       page,
		Limit:      limit,
		Total:      total,
		TotalPages: (total + int64(limit) - 1) / int64(limit),
	}

	c.JSON(http.StatusOK, response)
}

// GetJobDefinition retrieves a specific job definition
// @Summary Get job definition by ID
// @Description Retrieve a specific job definition by its ID
// @Tags jobs
// @Produce json
// @Param id path string true "Job definition ID"
// @Success 200 {object} models.JobDefinition
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/jobs/{id} [get]
func (h *JobHandler) GetJobDefinition(c *gin.Context) {
	id := c.Param("id")
	jobID, err := uuid.Parse(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "invalid job ID"})
		return
	}

	var jobDef models.JobDefinition
	if err := h.db.DB.Preload("Owner").
		Preload("ResourcePool").
		Preload("Schedules").
		First(&jobDef, jobID).Error; err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{Error: "job definition not found"})
		return
	}

	c.JSON(http.StatusOK, jobDef)
}

// UpdateJobDefinition updates an existing job definition
// @Summary Update job definition
// @Description Update an existing job definition
// @Tags jobs
// @Accept json
// @Produce json
// @Param id path string true "Job definition ID"
// @Param job body models.JobDefinition true "Updated job definition"
// @Success 200 {object} models.JobDefinition
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/jobs/{id} [put]
func (h *JobHandler) UpdateJobDefinition(c *gin.Context) {
	id := c.Param("id")
	jobID, err := uuid.Parse(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "invalid job ID"})
		return
	}

	var existingJob models.JobDefinition
	if err := h.db.DB.First(&existingJob, jobID).Error; err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{Error: "job definition not found"})
		return
	}

	var updateData models.JobDefinition
	if err := c.ShouldBindJSON(&updateData); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// Preserve ID and creation timestamp
	updateData.ID = jobID
	updateData.CreatedAt = existingJob.CreatedAt

	if err := h.db.DB.Save(&updateData).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "failed to update job definition"})
		return
	}

	c.JSON(http.StatusOK, updateData)
}

// DeleteJobDefinition deletes a job definition
// @Summary Delete job definition
// @Description Delete a job definition by ID
// @Tags jobs
// @Param id path string true "Job definition ID"
// @Success 204
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/jobs/{id} [delete]
func (h *JobHandler) DeleteJobDefinition(c *gin.Context) {
	id := c.Param("id")
	jobID, err := uuid.Parse(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "invalid job ID"})
		return
	}

	if err := h.db.DB.Delete(&models.JobDefinition{}, jobID).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "failed to delete job definition"})
		return
	}

	c.Status(http.StatusNoContent)
}

// TriggerJob manually triggers a job execution
// @Summary Trigger job execution
// @Description Manually trigger execution of a job definition
// @Tags jobs
// @Accept json
// @Produce json
// @Param id path string true "Job definition ID"
// @Param request body TriggerJobRequest true "Trigger request"
// @Success 201 {object} models.JobInstance
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/jobs/{id}/trigger [post]
func (h *JobHandler) TriggerJob(c *gin.Context) {
	id := c.Param("id")
	jobID, err := uuid.Parse(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "invalid job ID"})
		return
	}

	var request TriggerJobRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// Verify job exists
	var jobDef models.JobDefinition
	if err := h.db.DB.First(&jobDef, jobID).Error; err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{Error: "job definition not found"})
		return
	}

	// Trigger job execution
	instance, err := h.scheduler.TriggerJob(jobID, request.Parameters)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusCreated, instance)
}

// GetJobInstances retrieves job instances with pagination
// @Summary Get job instances
// @Description Retrieve a list of job instances with optional filtering and pagination
// @Tags jobs
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param status query string false "Filter by status"
// @Param job_id query string false "Filter by job definition ID"
// @Success 200 {object} PaginatedResponse{data=[]models.JobInstance}
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/job-instances [get]
func (h *JobHandler) GetJobInstances(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	status := c.Query("status")
	jobIDStr := c.Query("job_id")

	offset := (page - 1) * limit

	query := h.db.DB.Model(&models.JobInstance{}).
		Preload("JobDefinition").
		Preload("ExecutionNode").
		Order("scheduled_at DESC")

	// Apply filters
	if status != "" {
		query = query.Where("status = ?", status)
	}
	if jobIDStr != "" {
		jobID, err := uuid.Parse(jobIDStr)
		if err == nil {
			query = query.Where("job_definition_id = ?", jobID)
		}
	}

	// Get total count
	var total int64
	query.Count(&total)

	// Get paginated results
	var instances []models.JobInstance
	if err := query.Offset(offset).Limit(limit).Find(&instances).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "failed to retrieve job instances"})
		return
	}

	response := PaginatedResponse{
		Data:       instances,
		Page:       page,
		Limit:      limit,
		Total:      total,
		TotalPages: (total + int64(limit) - 1) / int64(limit),
	}

	c.JSON(http.StatusOK, response)
}

// GetJobInstance retrieves a specific job instance
// @Summary Get job instance by ID
// @Description Retrieve a specific job instance by its ID
// @Tags jobs
// @Produce json
// @Param id path string true "Job instance ID"
// @Success 200 {object} models.JobInstance
// @Failure 404 {object} ErrorResponse
// @Router /api/v1/job-instances/{id} [get]
func (h *JobHandler) GetJobInstance(c *gin.Context) {
	id := c.Param("id")
	instanceID, err := uuid.Parse(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "invalid instance ID"})
		return
	}

	var instance models.JobInstance
	if err := h.db.DB.Preload("JobDefinition").
		Preload("ExecutionNode").
		Preload("Schedule").
		First(&instance, instanceID).Error; err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{Error: "job instance not found"})
		return
	}

	c.JSON(http.StatusOK, instance)
}

// CancelJobInstance cancels a running job instance
// @Summary Cancel job instance
// @Description Cancel a running job instance
// @Tags jobs
// @Param id path string true "Job instance ID"
// @Success 200 {object} models.JobInstance
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/job-instances/{id}/cancel [post]
func (h *JobHandler) CancelJobInstance(c *gin.Context) {
	id := c.Param("id")
	instanceID, err := uuid.Parse(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "invalid instance ID"})
		return
	}

	var instance models.JobInstance
	if err := h.db.DB.First(&instance, instanceID).Error; err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{Error: "job instance not found"})
		return
	}

	// Update status to cancelled
	instance.Status = models.JobStatusCancelled
	if err := h.db.DB.Save(&instance).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "failed to cancel job instance"})
		return
	}

	c.JSON(http.StatusOK, instance)
}

// Request/Response types
type TriggerJobRequest struct {
	Parameters map[string]interface{} `json:"parameters"`
}

type ErrorResponse struct {
	Error string `json:"error"`
}

type PaginatedResponse struct {
	Data       interface{} `json:"data"`
	Page       int         `json:"page"`
	Limit      int         `json:"limit"`
	Total      int64       `json:"total"`
	TotalPages int64       `json:"total_pages"`
}

// Helper function to get user ID from context
func getUserIDFromContext(c *gin.Context) *uuid.UUID {
	if userID, exists := c.Get("user_id"); exists {
		if id, ok := userID.(uuid.UUID); ok {
			return &id
		}
	}
	return nil
}
