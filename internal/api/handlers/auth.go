package handlers

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"golang.org/x/crypto/bcrypt"
	"github.com/workflowmaster/workflowmaster/internal/api/middleware"
	"github.com/workflowmaster/workflowmaster/internal/database"
	"github.com/workflowmaster/workflowmaster/internal/models"
)

// <PERSON>th<PERSON><PERSON><PERSON> handles authentication requests
type AuthHandler struct {
	db            *database.Database
	authMiddleware *middleware.AuthMiddleware
}

// NewAuthHandler creates a new authentication handler
func NewAuthHandler(db *database.Database, jwtSecret string) *AuthHandler {
	return &AuthHandler{
		db:            db,
		authMiddleware: middleware.NewAuthMiddleware(jwtSecret, db),
	}
}

// LoginRequest represents a login request
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// RegisterRequest represents a registration request
type RegisterRequest struct {
	Username  string `json:"username" binding:"required"`
	Email     string `json:"email" binding:"required,email"`
	Password  string `json:"password" binding:"required,min=8"`
	FirstName string `json:"first_name"`
	LastName  string `json:"last_name"`
}

// RefreshTokenRequest represents a token refresh request
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required"`
}

// AuthResponse represents an authentication response
type AuthResponse struct {
	AccessToken  string      `json:"access_token"`
	RefreshToken string      `json:"refresh_token"`
	TokenType    string      `json:"token_type"`
	ExpiresIn    int         `json:"expires_in"`
	User         *UserInfo   `json:"user"`
}

// UserInfo represents user information in auth response
type UserInfo struct {
	ID        string   `json:"id"`
	Username  string   `json:"username"`
	Email     string   `json:"email"`
	FirstName *string  `json:"first_name"`
	LastName  *string  `json:"last_name"`
	IsAdmin   bool     `json:"is_admin"`
	Roles     []string `json:"roles"`
}

// Login authenticates a user and returns JWT tokens
// @Summary User login
// @Description Authenticate user with username/email and password
// @Tags auth
// @Accept json
// @Produce json
// @Param credentials body LoginRequest true "Login credentials"
// @Success 200 {object} AuthResponse
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/auth/login [post]
func (h *AuthHandler) Login(c *gin.Context) {
	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// Find user by username or email
	var user models.User
	err := h.db.DB.Where("username = ? OR email = ?", req.Username, req.Username).
		Preload("Roles").
		First(&user).Error
	if err != nil {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "invalid credentials"})
		return
	}

	// Check if user is active
	if !user.IsActive {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "account is disabled"})
		return
	}

	// Verify password
	if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(req.Password)); err != nil {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "invalid credentials"})
		return
	}

	// Update last login time
	now := time.Now().UTC()
	user.LastLoginAt = &now
	h.db.DB.Save(&user)

	// Generate tokens
	accessToken, err := h.authMiddleware.GenerateToken(&user, 24*time.Hour)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "failed to generate access token"})
		return
	}

	refreshToken, err := h.authMiddleware.GenerateToken(&user, 7*24*time.Hour)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "failed to generate refresh token"})
		return
	}

	// Prepare user info
	roles := make([]string, len(user.Roles))
	for i, role := range user.Roles {
		roles[i] = role.Name
	}

	userInfo := &UserInfo{
		ID:        user.ID.String(),
		Username:  user.Username,
		Email:     user.Email,
		FirstName: user.FirstName,
		LastName:  user.LastName,
		IsAdmin:   user.IsAdmin,
		Roles:     roles,
	}

	response := AuthResponse{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		TokenType:    "Bearer",
		ExpiresIn:    24 * 3600, // 24 hours in seconds
		User:         userInfo,
	}

	c.JSON(http.StatusOK, response)
}

// Register creates a new user account
// @Summary User registration
// @Description Register a new user account
// @Tags auth
// @Accept json
// @Produce json
// @Param user body RegisterRequest true "User registration data"
// @Success 201 {object} AuthResponse
// @Failure 400 {object} ErrorResponse
// @Failure 409 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/auth/register [post]
func (h *AuthHandler) Register(c *gin.Context) {
	var req RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// Check if username or email already exists
	var existingUser models.User
	err := h.db.DB.Where("username = ? OR email = ?", req.Username, req.Email).First(&existingUser).Error
	if err == nil {
		c.JSON(http.StatusConflict, ErrorResponse{Error: "username or email already exists"})
		return
	}

	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "failed to hash password"})
		return
	}

	// Create user
	user := models.User{
		Username:     req.Username,
		Email:        req.Email,
		PasswordHash: string(hashedPassword),
		FirstName:    stringPtr(req.FirstName),
		LastName:     stringPtr(req.LastName),
		IsActive:     true,
		IsAdmin:      false,
	}

	if err := h.db.DB.Create(&user).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "failed to create user"})
		return
	}

	// Assign default user role
	var userRole models.Role
	if err := h.db.DB.Where("name = ?", "user").First(&userRole).Error; err == nil {
		userRoleAssignment := models.UserRole{
			UserID: user.ID,
			RoleID: userRole.ID,
		}
		h.db.DB.Create(&userRoleAssignment)
	}

	// Load user with roles for response
	h.db.DB.Preload("Roles").First(&user, user.ID)

	// Generate tokens
	accessToken, err := h.authMiddleware.GenerateToken(&user, 24*time.Hour)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "failed to generate access token"})
		return
	}

	refreshToken, err := h.authMiddleware.GenerateToken(&user, 7*24*time.Hour)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "failed to generate refresh token"})
		return
	}

	// Prepare user info
	roles := make([]string, len(user.Roles))
	for i, role := range user.Roles {
		roles[i] = role.Name
	}

	userInfo := &UserInfo{
		ID:        user.ID.String(),
		Username:  user.Username,
		Email:     user.Email,
		FirstName: user.FirstName,
		LastName:  user.LastName,
		IsAdmin:   user.IsAdmin,
		Roles:     roles,
	}

	response := AuthResponse{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		TokenType:    "Bearer",
		ExpiresIn:    24 * 3600, // 24 hours in seconds
		User:         userInfo,
	}

	c.JSON(http.StatusCreated, response)
}

// RefreshToken refreshes an access token using a refresh token
// @Summary Refresh access token
// @Description Refresh an access token using a valid refresh token
// @Tags auth
// @Accept json
// @Produce json
// @Param token body RefreshTokenRequest true "Refresh token"
// @Success 200 {object} AuthResponse
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/auth/refresh [post]
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	var req RefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// Refresh the token
	newAccessToken, err := h.authMiddleware.RefreshToken(req.RefreshToken, 24*time.Hour)
	if err != nil {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "invalid or expired refresh token"})
		return
	}

	// Generate new refresh token
	newRefreshToken, err := h.authMiddleware.RefreshToken(req.RefreshToken, 7*24*time.Hour)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "failed to generate new refresh token"})
		return
	}

	response := AuthResponse{
		AccessToken:  newAccessToken,
		RefreshToken: newRefreshToken,
		TokenType:    "Bearer",
		ExpiresIn:    24 * 3600, // 24 hours in seconds
	}

	c.JSON(http.StatusOK, response)
}

// Logout logs out a user (revokes tokens)
// @Summary User logout
// @Description Logout user and revoke tokens
// @Tags auth
// @Security BearerAuth
// @Success 200 {object} map[string]string
// @Failure 401 {object} ErrorResponse
// @Router /api/v1/auth/logout [post]
func (h *AuthHandler) Logout(c *gin.Context) {
	// TODO: Implement token revocation
	// For now, just return success as tokens will expire naturally
	c.JSON(http.StatusOK, gin.H{"message": "logged out successfully"})
}

// GetProfile returns the current user's profile
// @Summary Get user profile
// @Description Get the current authenticated user's profile
// @Tags auth
// @Security BearerAuth
// @Produce json
// @Success 200 {object} UserInfo
// @Failure 401 {object} ErrorResponse
// @Router /api/v1/auth/profile [get]
func (h *AuthHandler) GetProfile(c *gin.Context) {
	user, exists := middleware.GetUserFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "user not authenticated"})
		return
	}

	// Load user with roles
	h.db.DB.Preload("Roles").First(user, user.ID)

	// Prepare user info
	roles := make([]string, len(user.Roles))
	for i, role := range user.Roles {
		roles[i] = role.Name
	}

	userInfo := &UserInfo{
		ID:        user.ID.String(),
		Username:  user.Username,
		Email:     user.Email,
		FirstName: user.FirstName,
		LastName:  user.LastName,
		IsAdmin:   user.IsAdmin,
		Roles:     roles,
	}

	c.JSON(http.StatusOK, userInfo)
}

// ChangePassword changes the current user's password
// @Summary Change password
// @Description Change the current user's password
// @Tags auth
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param password body ChangePasswordRequest true "Password change data"
// @Success 200 {object} map[string]string
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/auth/change-password [post]
func (h *AuthHandler) ChangePassword(c *gin.Context) {
	var req ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	user, exists := middleware.GetUserFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "user not authenticated"})
		return
	}

	// Verify current password
	if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(req.CurrentPassword)); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "current password is incorrect"})
		return
	}

	// Hash new password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "failed to hash new password"})
		return
	}

	// Update password
	user.PasswordHash = string(hashedPassword)
	if err := h.db.DB.Save(user).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "failed to update password"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "password changed successfully"})
}

// ChangePasswordRequest represents a password change request
type ChangePasswordRequest struct {
	CurrentPassword string `json:"current_password" binding:"required"`
	NewPassword     string `json:"new_password" binding:"required,min=8"`
}

// Helper function to create string pointers
func stringPtr(s string) *string {
	if s == "" {
		return nil
	}
	return &s
}
