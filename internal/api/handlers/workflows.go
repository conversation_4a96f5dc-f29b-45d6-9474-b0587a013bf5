package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/workflowmaster/workflowmaster/internal/database"
	"github.com/workflowmaster/workflowmaster/internal/models"
	"github.com/workflowmaster/workflowmaster/internal/workflow"
)

// WorkflowHandler handles workflow-related API requests
type WorkflowHandler struct {
	db     *database.Database
	engine *workflow.WorkflowEngine
}

// NewWorkflowHandler creates a new workflow handler
func NewWorkflowHandler(db *database.Database, engine *workflow.WorkflowEngine) *WorkflowHandler {
	return &WorkflowHandler{
		db:     db,
		engine: engine,
	}
}

// CreateWorkflow creates a new workflow definition
// @Summary Create a new workflow
// @Description Create a new workflow definition with the provided details
// @Tags workflows
// @Accept json
// @Produce json
// @Param workflow body models.Workflow true "Workflow definition"
// @Success 201 {object} models.Workflow
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/workflows [post]
func (h *WorkflowHandler) CreateWorkflow(c *gin.Context) {
	var workflow models.Workflow
	if err := c.ShouldBindJSON(&workflow); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// Set owner from authenticated user
	userID := getUserIDFromContext(c)
	if userID != nil {
		workflow.OwnerID = userID
	}

	// Validate required fields
	if workflow.Name == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "workflow name is required"})
		return
	}
	if workflow.Definition == nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "workflow definition is required"})
		return
	}

	// Create workflow
	if err := h.db.DB.Create(&workflow).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "failed to create workflow"})
		return
	}

	c.JSON(http.StatusCreated, workflow)
}

// GetWorkflows retrieves workflows with pagination
// @Summary Get workflows
// @Description Retrieve a list of workflows with optional filtering and pagination
// @Tags workflows
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param active query bool false "Filter by active status"
// @Success 200 {object} PaginatedResponse{data=[]models.Workflow}
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/workflows [get]
func (h *WorkflowHandler) GetWorkflows(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	activeStr := c.Query("active")

	offset := (page - 1) * limit

	query := h.db.DB.Model(&models.Workflow{}).
		Preload("Owner")

	// Apply filters
	if activeStr != "" {
		active, _ := strconv.ParseBool(activeStr)
		query = query.Where("is_active = ?", active)
	}

	// Get total count
	var total int64
	query.Count(&total)

	// Get paginated results
	var workflows []models.Workflow
	if err := query.Offset(offset).Limit(limit).Find(&workflows).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "failed to retrieve workflows"})
		return
	}

	response := PaginatedResponse{
		Data:       workflows,
		Page:       page,
		Limit:      limit,
		Total:      total,
		TotalPages: (total + int64(limit) - 1) / int64(limit),
	}

	c.JSON(http.StatusOK, response)
}

// GetWorkflow retrieves a specific workflow
// @Summary Get workflow by ID
// @Description Retrieve a specific workflow by its ID
// @Tags workflows
// @Produce json
// @Param id path string true "Workflow ID"
// @Success 200 {object} models.Workflow
// @Failure 404 {object} ErrorResponse
// @Router /api/v1/workflows/{id} [get]
func (h *WorkflowHandler) GetWorkflow(c *gin.Context) {
	id := c.Param("id")
	workflowID, err := uuid.Parse(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "invalid workflow ID"})
		return
	}

	var workflow models.Workflow
	if err := h.db.DB.Preload("Owner").
		First(&workflow, workflowID).Error; err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{Error: "workflow not found"})
		return
	}

	c.JSON(http.StatusOK, workflow)
}

// UpdateWorkflow updates an existing workflow
// @Summary Update workflow
// @Description Update an existing workflow
// @Tags workflows
// @Accept json
// @Produce json
// @Param id path string true "Workflow ID"
// @Param workflow body models.Workflow true "Updated workflow"
// @Success 200 {object} models.Workflow
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/workflows/{id} [put]
func (h *WorkflowHandler) UpdateWorkflow(c *gin.Context) {
	id := c.Param("id")
	workflowID, err := uuid.Parse(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "invalid workflow ID"})
		return
	}

	var existingWorkflow models.Workflow
	if err := h.db.DB.First(&existingWorkflow, workflowID).Error; err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{Error: "workflow not found"})
		return
	}

	var updateData models.Workflow
	if err := c.ShouldBindJSON(&updateData); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// Preserve ID and creation timestamp
	updateData.ID = workflowID
	updateData.CreatedAt = existingWorkflow.CreatedAt

	if err := h.db.DB.Save(&updateData).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "failed to update workflow"})
		return
	}

	c.JSON(http.StatusOK, updateData)
}

// DeleteWorkflow deletes a workflow
// @Summary Delete workflow
// @Description Delete a workflow by ID
// @Tags workflows
// @Param id path string true "Workflow ID"
// @Success 204
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/workflows/{id} [delete]
func (h *WorkflowHandler) DeleteWorkflow(c *gin.Context) {
	id := c.Param("id")
	workflowID, err := uuid.Parse(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "invalid workflow ID"})
		return
	}

	if err := h.db.DB.Delete(&models.Workflow{}, workflowID).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "failed to delete workflow"})
		return
	}

	c.Status(http.StatusNoContent)
}

// ExecuteWorkflow triggers workflow execution
// @Summary Execute workflow
// @Description Trigger execution of a workflow
// @Tags workflows
// @Accept json
// @Produce json
// @Param id path string true "Workflow ID"
// @Param request body ExecuteWorkflowRequest true "Execution request"
// @Success 201 {object} models.WorkflowInstance
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/workflows/{id}/execute [post]
func (h *WorkflowHandler) ExecuteWorkflow(c *gin.Context) {
	id := c.Param("id")
	workflowID, err := uuid.Parse(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "invalid workflow ID"})
		return
	}

	var request ExecuteWorkflowRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// Get user ID from context
	userID := getUserIDFromContext(c)

	// Execute workflow
	instance, err := h.engine.ExecuteWorkflow(workflowID, request.Parameters, userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusCreated, instance)
}

// GetWorkflowInstances retrieves workflow instances with pagination
// @Summary Get workflow instances
// @Description Retrieve a list of workflow instances with optional filtering and pagination
// @Tags workflows
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param status query string false "Filter by status"
// @Param workflow_id query string false "Filter by workflow ID"
// @Success 200 {object} PaginatedResponse{data=[]models.WorkflowInstance}
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/workflow-instances [get]
func (h *WorkflowHandler) GetWorkflowInstances(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	status := c.Query("status")
	workflowIDStr := c.Query("workflow_id")

	offset := (page - 1) * limit

	query := h.db.DB.Model(&models.WorkflowInstance{}).
		Preload("Workflow").
		Preload("Creator").
		Order("scheduled_at DESC")

	// Apply filters
	if status != "" {
		query = query.Where("status = ?", status)
	}
	if workflowIDStr != "" {
		workflowID, err := uuid.Parse(workflowIDStr)
		if err == nil {
			query = query.Where("workflow_id = ?", workflowID)
		}
	}

	// Get total count
	var total int64
	query.Count(&total)

	// Get paginated results
	var instances []models.WorkflowInstance
	if err := query.Offset(offset).Limit(limit).Find(&instances).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "failed to retrieve workflow instances"})
		return
	}

	response := PaginatedResponse{
		Data:       instances,
		Page:       page,
		Limit:      limit,
		Total:      total,
		TotalPages: (total + int64(limit) - 1) / int64(limit),
	}

	c.JSON(http.StatusOK, response)
}

// GetWorkflowInstance retrieves a specific workflow instance
// @Summary Get workflow instance by ID
// @Description Retrieve a specific workflow instance by its ID
// @Tags workflows
// @Produce json
// @Param id path string true "Workflow instance ID"
// @Success 200 {object} models.WorkflowInstance
// @Failure 404 {object} ErrorResponse
// @Router /api/v1/workflow-instances/{id} [get]
func (h *WorkflowHandler) GetWorkflowInstance(c *gin.Context) {
	id := c.Param("id")
	instanceID, err := uuid.Parse(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "invalid instance ID"})
		return
	}

	var instance models.WorkflowInstance
	if err := h.db.DB.Preload("Workflow").
		Preload("Creator").
		Preload("JobInstances").
		First(&instance, instanceID).Error; err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{Error: "workflow instance not found"})
		return
	}

	c.JSON(http.StatusOK, instance)
}

// CancelWorkflowInstance cancels a running workflow instance
// @Summary Cancel workflow instance
// @Description Cancel a running workflow instance
// @Tags workflows
// @Param id path string true "Workflow instance ID"
// @Success 200 {object} models.WorkflowInstance
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/workflow-instances/{id}/cancel [post]
func (h *WorkflowHandler) CancelWorkflowInstance(c *gin.Context) {
	id := c.Param("id")
	instanceID, err := uuid.Parse(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "invalid instance ID"})
		return
	}

	// Cancel workflow execution
	if err := h.engine.CancelWorkflow(instanceID); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	// Return updated instance
	var instance models.WorkflowInstance
	if err := h.db.DB.First(&instance, instanceID).Error; err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{Error: "workflow instance not found"})
		return
	}

	c.JSON(http.StatusOK, instance)
}

// GetWorkflowStatus returns the current status of running workflows
// @Summary Get workflow status
// @Description Get the current status of all running workflows
// @Tags workflows
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/workflows/status [get]
func (h *WorkflowHandler) GetWorkflowStatus(c *gin.Context) {
	runningWorkflows := h.engine.GetRunningWorkflows()
	
	status := map[string]interface{}{
		"running_workflows": len(runningWorkflows),
		"workflows":         make([]map[string]interface{}, 0),
	}

	for instanceID, execution := range runningWorkflows {
		workflowInfo := map[string]interface{}{
			"instance_id":   instanceID,
			"workflow_name": execution.Definition.Name,
			"start_time":    execution.StartTime,
			"job_states":    execution.JobStates,
		}
		status["workflows"] = append(status["workflows"].([]map[string]interface{}), workflowInfo)
	}

	c.JSON(http.StatusOK, status)
}

// Request types
type ExecuteWorkflowRequest struct {
	Parameters map[string]interface{} `json:"parameters"`
}
