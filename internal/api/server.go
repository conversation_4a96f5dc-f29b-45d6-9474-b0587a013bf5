package api

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/workflowmaster/workflowmaster/internal/api/handlers"
	"github.com/workflowmaster/workflowmaster/internal/api/middleware"
	"github.com/workflowmaster/workflowmaster/internal/database"
	"github.com/workflowmaster/workflowmaster/internal/scheduler"
	"github.com/workflowmaster/workflowmaster/internal/workflow"
)

// Server represents the API server
type Server struct {
	router    *gin.Engine
	server    *http.Server
	db        *database.Database
	scheduler *scheduler.Scheduler
	workflow  *workflow.WorkflowEngine
}

// Config holds server configuration
type Config struct {
	Host         string
	Port         int
	Debug        bool
	CORSOrigins  []string
	JWTSecret    string
	ReadTimeout  time.Duration
	WriteTimeout time.Duration
}

// NewServer creates a new API server
func NewServer(config Config, db *database.Database, scheduler *scheduler.Scheduler, workflowEngine *workflow.WorkflowEngine) *Server {
	// Set Gin mode
	if !config.Debug {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.New()

	// Add middleware
	router.Use(gin.Logger())
	router.Use(gin.Recovery())

	// CORS configuration
	corsConfig := cors.DefaultConfig()
	if len(config.CORSOrigins) > 0 {
		corsConfig.AllowOrigins = config.CORSOrigins
	} else {
		corsConfig.AllowAllOrigins = true
	}
	corsConfig.AllowHeaders = []string{"Origin", "Content-Length", "Content-Type", "Authorization"}
	corsConfig.AllowMethods = []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}
	router.Use(cors.New(corsConfig))

	server := &Server{
		router:    router,
		db:        db,
		scheduler: scheduler,
		workflow:  workflowEngine,
		server: &http.Server{
			Addr:         fmt.Sprintf("%s:%d", config.Host, config.Port),
			Handler:      router,
			ReadTimeout:  config.ReadTimeout,
			WriteTimeout: config.WriteTimeout,
		},
	}

	// Setup routes
	server.setupRoutes(config.JWTSecret)

	return server
}

// setupRoutes configures all API routes
func (s *Server) setupRoutes(jwtSecret string) {
	// Health check endpoint
	s.router.GET("/health", s.healthCheck)

	// API version 1
	v1 := s.router.Group("/api/v1")

	// Authentication middleware for protected routes
	authMiddleware := middleware.NewAuthMiddleware(jwtSecret, s.db)

	// Public routes (no authentication required)
	public := v1.Group("/")
	{
		// Authentication endpoints
		authHandler := handlers.NewAuthHandler(s.db, jwtSecret)
		public.POST("/auth/login", authHandler.Login)
		public.POST("/auth/register", authHandler.Register)
		public.POST("/auth/refresh", authHandler.RefreshToken)
	}

	// Protected routes (authentication required)
	protected := v1.Group("/")
	protected.Use(authMiddleware.RequireAuth())
	{
		// Job management
		jobHandler := handlers.NewJobHandler(s.db, s.scheduler)
		jobs := protected.Group("/jobs")
		{
			jobs.POST("", jobHandler.CreateJobDefinition)
			jobs.GET("", jobHandler.GetJobDefinitions)
			jobs.GET("/:id", jobHandler.GetJobDefinition)
			jobs.PUT("/:id", jobHandler.UpdateJobDefinition)
			jobs.DELETE("/:id", jobHandler.DeleteJobDefinition)
			jobs.POST("/:id/trigger", jobHandler.TriggerJob)
		}

		// Job instances
		instances := protected.Group("/job-instances")
		{
			instances.GET("", jobHandler.GetJobInstances)
			instances.GET("/:id", jobHandler.GetJobInstance)
			instances.POST("/:id/cancel", jobHandler.CancelJobInstance)
		}

		// Workflow management
		workflowHandler := handlers.NewWorkflowHandler(s.db, s.workflow)
		workflows := protected.Group("/workflows")
		{
			workflows.POST("", workflowHandler.CreateWorkflow)
			workflows.GET("", workflowHandler.GetWorkflows)
			workflows.GET("/:id", workflowHandler.GetWorkflow)
			workflows.PUT("/:id", workflowHandler.UpdateWorkflow)
			workflows.DELETE("/:id", workflowHandler.DeleteWorkflow)
			workflows.POST("/:id/execute", workflowHandler.ExecuteWorkflow)
			workflows.GET("/status", workflowHandler.GetWorkflowStatus)
		}

		// Workflow instances
		workflowInstances := protected.Group("/workflow-instances")
		{
			workflowInstances.GET("", workflowHandler.GetWorkflowInstances)
			workflowInstances.GET("/:id", workflowHandler.GetWorkflowInstance)
			workflowInstances.POST("/:id/cancel", workflowHandler.CancelWorkflowInstance)
		}

		// Schedule management
		scheduleHandler := handlers.NewScheduleHandler(s.db, s.scheduler)
		schedules := protected.Group("/schedules")
		{
			schedules.POST("", scheduleHandler.CreateSchedule)
			schedules.GET("", scheduleHandler.GetSchedules)
			schedules.GET("/:id", scheduleHandler.GetSchedule)
			schedules.PUT("/:id", scheduleHandler.UpdateSchedule)
			schedules.DELETE("/:id", scheduleHandler.DeleteSchedule)
		}

		// Resource management
		resourceHandler := handlers.NewResourceHandler(s.db)
		resources := protected.Group("/resources")
		{
			// Resource pools
			pools := resources.Group("/pools")
			{
				pools.POST("", resourceHandler.CreateResourcePool)
				pools.GET("", resourceHandler.GetResourcePools)
				pools.GET("/:id", resourceHandler.GetResourcePool)
				pools.PUT("/:id", resourceHandler.UpdateResourcePool)
				pools.DELETE("/:id", resourceHandler.DeleteResourcePool)
			}

			// Execution nodes
			nodes := resources.Group("/nodes")
			{
				nodes.GET("", resourceHandler.GetExecutionNodes)
				nodes.GET("/:id", resourceHandler.GetExecutionNode)
				nodes.PUT("/:id", resourceHandler.UpdateExecutionNode)
				nodes.DELETE("/:id", resourceHandler.DeleteExecutionNode)
			}
		}

		// User management (admin only)
		userHandler := handlers.NewUserHandler(s.db)
		users := protected.Group("/users")
		users.Use(authMiddleware.RequireAdmin())
		{
			users.POST("", userHandler.CreateUser)
			users.GET("", userHandler.GetUsers)
			users.GET("/:id", userHandler.GetUser)
			users.PUT("/:id", userHandler.UpdateUser)
			users.DELETE("/:id", userHandler.DeleteUser)
		}

		// Role management (admin only)
		roles := protected.Group("/roles")
		roles.Use(authMiddleware.RequireAdmin())
		{
			roles.POST("", userHandler.CreateRole)
			roles.GET("", userHandler.GetRoles)
			roles.GET("/:id", userHandler.GetRole)
			roles.PUT("/:id", userHandler.UpdateRole)
			roles.DELETE("/:id", userHandler.DeleteRole)
		}

		// System monitoring and statistics
		system := protected.Group("/system")
		{
			system.GET("/stats", s.getSystemStats)
			system.GET("/health", s.getSystemHealth)
		}

		// Event management
		eventHandler := handlers.NewEventHandler(s.db, s.scheduler)
		events := protected.Group("/events")
		{
			events.POST("", eventHandler.CreateEvent)
			events.GET("", eventHandler.GetEvents)
			events.GET("/:id", eventHandler.GetEvent)
		}

		// Event triggers
		triggers := protected.Group("/triggers")
		{
			triggers.POST("", eventHandler.CreateEventTrigger)
			triggers.GET("", eventHandler.GetEventTriggers)
			triggers.GET("/:id", eventHandler.GetEventTrigger)
			triggers.PUT("/:id", eventHandler.UpdateEventTrigger)
			triggers.DELETE("/:id", eventHandler.DeleteEventTrigger)
		}

		// Calendar management
		calendarHandler := handlers.NewCalendarHandler(s.db)
		calendars := protected.Group("/calendars")
		{
			calendars.POST("", calendarHandler.CreateCalendar)
			calendars.GET("", calendarHandler.GetCalendars)
			calendars.GET("/:id", calendarHandler.GetCalendar)
			calendars.PUT("/:id", calendarHandler.UpdateCalendar)
			calendars.DELETE("/:id", calendarHandler.DeleteCalendar)
		}

		// SLA management
		slaHandler := handlers.NewSLAHandler(s.db)
		slas := protected.Group("/slas")
		{
			slas.POST("", slaHandler.CreateSLADefinition)
			slas.GET("", slaHandler.GetSLADefinitions)
			slas.GET("/:id", slaHandler.GetSLADefinition)
			slas.PUT("/:id", slaHandler.UpdateSLADefinition)
			slas.DELETE("/:id", slaHandler.DeleteSLADefinition)
		}

		// Audit logs (admin only)
		auditHandler := handlers.NewAuditHandler(s.db)
		audit := protected.Group("/audit")
		audit.Use(authMiddleware.RequireAdmin())
		{
			audit.GET("/logs", auditHandler.GetAuditLogs)
			audit.GET("/logs/:id", auditHandler.GetAuditLog)
		}

		// Notifications
		notificationHandler := handlers.NewNotificationHandler(s.db)
		notifications := protected.Group("/notifications")
		{
			notifications.GET("", notificationHandler.GetNotifications)
			notifications.GET("/:id", notificationHandler.GetNotification)
			notifications.PUT("/:id/read", notificationHandler.MarkAsRead)
		}
	}

	// WebSocket endpoints for real-time updates
	ws := s.router.Group("/ws")
	{
		wsHandler := handlers.NewWebSocketHandler(s.db)
		ws.GET("/jobs", wsHandler.JobUpdates)
		ws.GET("/workflows", wsHandler.WorkflowUpdates)
		ws.GET("/system", wsHandler.SystemUpdates)
	}

	// Serve static files for the web UI
	s.router.Static("/static", "./web/dist/static")
	s.router.StaticFile("/", "./web/dist/index.html")
	s.router.NoRoute(func(c *gin.Context) {
		c.File("./web/dist/index.html")
	})
}

// Start starts the API server
func (s *Server) Start() error {
	log.Printf("Starting API server on %s", s.server.Addr)
	return s.server.ListenAndServe()
}

// Stop gracefully stops the API server
func (s *Server) Stop(ctx context.Context) error {
	log.Println("Stopping API server...")
	return s.server.Shutdown(ctx)
}

// healthCheck handles health check requests
func (s *Server) healthCheck(c *gin.Context) {
	// Check database connectivity
	if err := s.db.Health(); err != nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"status":  "unhealthy",
			"error":   "database connection failed",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status":    "healthy",
		"timestamp": time.Now().UTC(),
		"version":   "1.0.0", // TODO: Get from build info
	})
}

// getSystemStats returns system statistics
func (s *Server) getSystemStats(c *gin.Context) {
	schedulerStats := s.scheduler.GetStats()
	
	// Get database stats
	var jobCount, workflowCount, instanceCount int64
	s.db.DB.Model(&handlers.JobDefinition{}).Count(&jobCount)
	s.db.DB.Model(&handlers.Workflow{}).Count(&workflowCount)
	s.db.DB.Model(&handlers.JobInstance{}).Count(&instanceCount)

	stats := gin.H{
		"scheduler":  schedulerStats,
		"database": gin.H{
			"job_definitions":    jobCount,
			"workflows":          workflowCount,
			"job_instances":      instanceCount,
		},
		"timestamp": time.Now().UTC(),
	}

	c.JSON(http.StatusOK, stats)
}

// getSystemHealth returns detailed system health information
func (s *Server) getSystemHealth(c *gin.Context) {
	health := gin.H{
		"database": gin.H{
			"status": "healthy",
		},
		"scheduler": gin.H{
			"status":  "healthy",
			"running": s.scheduler.IsRunning(),
		},
		"workflow_engine": gin.H{
			"status": "healthy",
		},
		"timestamp": time.Now().UTC(),
	}

	// Check database health
	if err := s.db.Health(); err != nil {
		health["database"] = gin.H{
			"status": "unhealthy",
			"error":  err.Error(),
		}
	}

	c.JSON(http.StatusOK, health)
}
