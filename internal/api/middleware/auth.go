package middleware

import (
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/workflowmaster/workflowmaster/internal/database"
	"github.com/workflowmaster/workflowmaster/internal/models"
)

// AuthMiddleware handles authentication and authorization
type AuthMiddleware struct {
	jwtSecret []byte
	db        *database.Database
}

// Claims represents JWT claims
type Claims struct {
	UserID   uuid.UUID `json:"user_id"`
	Username string    `json:"username"`
	IsAdmin  bool      `json:"is_admin"`
	jwt.RegisteredClaims
}

// NewAuthMiddleware creates a new authentication middleware
func NewAuthMiddleware(jwtSecret string, db *database.Database) *AuthMiddleware {
	return &AuthMiddleware{
		jwtSecret: []byte(jwtSecret),
		db:        db,
	}
}

// RequireAuth middleware that requires authentication
func (am *AuthMiddleware) RequireAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		token, err := am.extractToken(c)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "missing or invalid token"})
			c.Abort()
			return
		}

		claims, err := am.validateToken(token)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "invalid token"})
			c.Abort()
			return
		}

		// Verify user still exists and is active
		var user models.User
		if err := am.db.DB.First(&user, claims.UserID).Error; err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "user not found"})
			c.Abort()
			return
		}

		if !user.IsActive {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "user account is disabled"})
			c.Abort()
			return
		}

		// Set user information in context
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("is_admin", claims.IsAdmin)
		c.Set("user", &user)

		c.Next()
	}
}

// RequireAdmin middleware that requires admin privileges
func (am *AuthMiddleware) RequireAdmin() gin.HandlerFunc {
	return func(c *gin.Context) {
		isAdmin, exists := c.Get("is_admin")
		if !exists || !isAdmin.(bool) {
			c.JSON(http.StatusForbidden, gin.H{"error": "admin privileges required"})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequirePermission middleware that requires specific permission
func (am *AuthMiddleware) RequirePermission(permission string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "user not authenticated"})
			c.Abort()
			return
		}

		hasPermission, err := am.checkUserPermission(userID.(uuid.UUID), permission)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to check permissions"})
			c.Abort()
			return
		}

		if !hasPermission {
			c.JSON(http.StatusForbidden, gin.H{"error": "insufficient permissions"})
			c.Abort()
			return
		}

		c.Next()
	}
}

// OptionalAuth middleware that optionally authenticates users
func (am *AuthMiddleware) OptionalAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		token, err := am.extractToken(c)
		if err != nil {
			// No token provided, continue without authentication
			c.Next()
			return
		}

		claims, err := am.validateToken(token)
		if err != nil {
			// Invalid token, continue without authentication
			c.Next()
			return
		}

		// Verify user still exists and is active
		var user models.User
		if err := am.db.DB.First(&user, claims.UserID).Error; err != nil {
			// User not found, continue without authentication
			c.Next()
			return
		}

		if !user.IsActive {
			// User inactive, continue without authentication
			c.Next()
			return
		}

		// Set user information in context
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("is_admin", claims.IsAdmin)
		c.Set("user", &user)

		c.Next()
	}
}

// extractToken extracts JWT token from request
func (am *AuthMiddleware) extractToken(c *gin.Context) (string, error) {
	// Try Authorization header first
	authHeader := c.GetHeader("Authorization")
	if authHeader != "" {
		parts := strings.SplitN(authHeader, " ", 2)
		if len(parts) == 2 && parts[0] == "Bearer" {
			return parts[1], nil
		}
	}

	// Try query parameter
	token := c.Query("token")
	if token != "" {
		return token, nil
	}

	// Try cookie
	token, err := c.Cookie("auth_token")
	if err == nil && token != "" {
		return token, nil
	}

	return "", jwt.ErrTokenMalformed
}

// validateToken validates and parses JWT token
func (am *AuthMiddleware) validateToken(tokenString string) (*Claims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		return am.jwtSecret, nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(*Claims); ok && token.Valid {
		return claims, nil
	}

	return nil, jwt.ErrTokenInvalid
}

// checkUserPermission checks if user has specific permission
func (am *AuthMiddleware) checkUserPermission(userID uuid.UUID, permission string) (bool, error) {
	// Get user with roles
	var user models.User
	err := am.db.DB.Preload("Roles").First(&user, userID).Error
	if err != nil {
		return false, err
	}

	// Admin users have all permissions
	if user.IsAdmin {
		return true, nil
	}

	// Check role permissions
	for _, role := range user.Roles {
		for _, perm := range role.Permissions {
			if perm == permission || perm == "*" {
				return true, nil
			}
		}
	}

	return false, nil
}

// GenerateToken generates a JWT token for a user
func (am *AuthMiddleware) GenerateToken(user *models.User, duration time.Duration) (string, error) {
	now := time.Now()
	claims := &Claims{
		UserID:   user.ID,
		Username: user.Username,
		IsAdmin:  user.IsAdmin,
		RegisteredClaims: jwt.RegisteredClaims{
			IssuedAt:  jwt.NewNumericDate(now),
			ExpiresAt: jwt.NewNumericDate(now.Add(duration)),
			NotBefore: jwt.NewNumericDate(now),
			Issuer:    "workflowmaster",
			Subject:   user.ID.String(),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(am.jwtSecret)
}

// RefreshToken refreshes an existing JWT token
func (am *AuthMiddleware) RefreshToken(tokenString string, duration time.Duration) (string, error) {
	claims, err := am.validateToken(tokenString)
	if err != nil {
		return "", err
	}

	// Check if token is close to expiry (within 1 hour)
	if time.Until(claims.ExpiresAt.Time) > time.Hour {
		return "", jwt.ErrTokenNotValidYet
	}

	// Get fresh user data
	var user models.User
	if err := am.db.DB.First(&user, claims.UserID).Error; err != nil {
		return "", err
	}

	if !user.IsActive {
		return "", jwt.ErrTokenInvalid
	}

	// Generate new token
	return am.GenerateToken(&user, duration)
}

// RevokeToken adds token to revocation list (if implemented)
func (am *AuthMiddleware) RevokeToken(tokenString string) error {
	// TODO: Implement token revocation list
	// For now, tokens will remain valid until expiry
	return nil
}

// GetUserFromContext extracts user from gin context
func GetUserFromContext(c *gin.Context) (*models.User, bool) {
	if user, exists := c.Get("user"); exists {
		if u, ok := user.(*models.User); ok {
			return u, true
		}
	}
	return nil, false
}

// GetUserIDFromContext extracts user ID from gin context
func GetUserIDFromContext(c *gin.Context) (uuid.UUID, bool) {
	if userID, exists := c.Get("user_id"); exists {
		if id, ok := userID.(uuid.UUID); ok {
			return id, true
		}
	}
	return uuid.Nil, false
}

// HasPermission checks if current user has specific permission
func (am *AuthMiddleware) HasPermission(c *gin.Context, permission string) bool {
	userID, exists := c.Get("user_id")
	if !exists {
		return false
	}

	hasPermission, err := am.checkUserPermission(userID.(uuid.UUID), permission)
	if err != nil {
		return false
	}

	return hasPermission
}
