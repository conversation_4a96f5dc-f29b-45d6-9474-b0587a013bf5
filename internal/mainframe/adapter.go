package mainframe

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/workflowmaster/workflowmaster/internal/database"
)

// MainframeAdapter is the main service for mainframe integration
type MainframeAdapter struct {
	db                *database.Database
	connectionManager *ConnectionManager
	protocolHandlers  map[string]ProtocolHandler
	jobManager        *JobManager
	dataManager       *DataManager
	monitoringManager *MonitoringManager
	securityManager   *SecurityManager
	
	// Configuration
	config            *AdapterConfig
	
	// State management
	running           bool
	ctx               context.Context
	cancel            context.CancelFunc
	mutex             sync.RWMutex
}

// AdapterConfig holds configuration for the mainframe adapter
type AdapterConfig struct {
	Enabled                bool          `yaml:"enabled" json:"enabled"`
	MaxConnections         int           `yaml:"max_connections" json:"max_connections"`
	ConnectionTimeout      time.Duration `yaml:"connection_timeout" json:"connection_timeout"`
	JobPollingInterval     time.Duration `yaml:"job_polling_interval" json:"job_polling_interval"`
	MonitoringInterval     time.Duration `yaml:"monitoring_interval" json:"monitoring_interval"`
	RetryAttempts          int           `yaml:"retry_attempts" json:"retry_attempts"`
	RetryDelay             time.Duration `yaml:"retry_delay" json:"retry_delay"`
	EnableSSL              bool          `yaml:"enable_ssl" json:"enable_ssl"`
	SSLVerifyPeer          bool          `yaml:"ssl_verify_peer" json:"ssl_verify_peer"`
	LogLevel               string        `yaml:"log_level" json:"log_level"`
	
	// Protocol-specific configurations
	FTPConfig              *FTPConfig    `yaml:"ftp" json:"ftp"`
	SFTPConfig             *SFTPConfig   `yaml:"sftp" json:"sftp"`
	MQConfig               *MQConfig     `yaml:"mq" json:"mq"`
	TelnetConfig           *TelnetConfig `yaml:"telnet" json:"telnet"`
	
	// Security configurations
	EncryptionKey          string        `yaml:"encryption_key" json:"-"`
	CertificatePath        string        `yaml:"certificate_path" json:"certificate_path"`
	PrivateKeyPath         string        `yaml:"private_key_path" json:"private_key_path"`
	TrustedCAPath          string        `yaml:"trusted_ca_path" json:"trusted_ca_path"`
}

// Protocol-specific configurations
type FTPConfig struct {
	PassiveMode    bool          `yaml:"passive_mode" json:"passive_mode"`
	BinaryMode     bool          `yaml:"binary_mode" json:"binary_mode"`
	Timeout        time.Duration `yaml:"timeout" json:"timeout"`
	KeepAlive      time.Duration `yaml:"keep_alive" json:"keep_alive"`
}

type SFTPConfig struct {
	KeyExchanges   []string      `yaml:"key_exchanges" json:"key_exchanges"`
	Ciphers        []string      `yaml:"ciphers" json:"ciphers"`
	MACs           []string      `yaml:"macs" json:"macs"`
	Timeout        time.Duration `yaml:"timeout" json:"timeout"`
}

type MQConfig struct {
	QueueManager   string        `yaml:"queue_manager" json:"queue_manager"`
	Channel        string        `yaml:"channel" json:"channel"`
	ConnName       string        `yaml:"conn_name" json:"conn_name"`
	UserID         string        `yaml:"user_id" json:"user_id"`
	Timeout        time.Duration `yaml:"timeout" json:"timeout"`
}

type TelnetConfig struct {
	LoginPrompt    string        `yaml:"login_prompt" json:"login_prompt"`
	PasswordPrompt string        `yaml:"password_prompt" json:"password_prompt"`
	CommandPrompt  string        `yaml:"command_prompt" json:"command_prompt"`
	Timeout        time.Duration `yaml:"timeout" json:"timeout"`
}

// ProtocolHandler interface for different communication protocols
type ProtocolHandler interface {
	Connect(connection *MainframeConnection) (Connection, error)
	Disconnect(conn Connection) error
	GetProtocolName() string
	ValidateConnection(connection *MainframeConnection) error
}

// Connection interface for active mainframe connections
type Connection interface {
	IsConnected() bool
	GetConnectionID() uuid.UUID
	GetLastActivity() time.Time
	Close() error
	ExecuteCommand(command string) (string, error)
	TransferFile(source, destination string, options TransferOptions) error
}

// TransferOptions holds options for file transfers
type TransferOptions struct {
	TransferMode    string // binary, ascii, ebcdic
	ConvertEncoding bool
	SourceEncoding  string
	TargetEncoding  string
	CreatePath      bool
	Overwrite       bool
	PreserveAttrs   bool
}

// NewMainframeAdapter creates a new mainframe adapter instance
func NewMainframeAdapter(db *database.Database, config *AdapterConfig) *MainframeAdapter {
	ctx, cancel := context.WithCancel(context.Background())
	
	adapter := &MainframeAdapter{
		db:               db,
		config:           config,
		protocolHandlers: make(map[string]ProtocolHandler),
		ctx:              ctx,
		cancel:           cancel,
		running:          false,
	}
	
	// Initialize managers
	adapter.connectionManager = NewConnectionManager(db, config)
	adapter.jobManager = NewJobManager(db, config)
	adapter.dataManager = NewDataManager(db, config)
	adapter.monitoringManager = NewMonitoringManager(db, config)
	adapter.securityManager = NewSecurityManager(config)
	
	// Register protocol handlers
	adapter.registerProtocolHandlers()
	
	return adapter
}

// Start starts the mainframe adapter service
func (ma *MainframeAdapter) Start() error {
	ma.mutex.Lock()
	defer ma.mutex.Unlock()
	
	if !ma.config.Enabled {
		log.Println("Mainframe adapter is disabled")
		return nil
	}
	
	if ma.running {
		return fmt.Errorf("mainframe adapter is already running")
	}
	
	log.Println("Starting mainframe adapter...")
	
	// Start connection manager
	if err := ma.connectionManager.Start(ma.ctx); err != nil {
		return fmt.Errorf("failed to start connection manager: %w", err)
	}
	
	// Start job manager
	if err := ma.jobManager.Start(ma.ctx); err != nil {
		return fmt.Errorf("failed to start job manager: %w", err)
	}
	
	// Start data manager
	if err := ma.dataManager.Start(ma.ctx); err != nil {
		return fmt.Errorf("failed to start data manager: %w", err)
	}
	
	// Start monitoring manager
	if err := ma.monitoringManager.Start(ma.ctx); err != nil {
		return fmt.Errorf("failed to start monitoring manager: %w", err)
	}
	
	ma.running = true
	log.Println("Mainframe adapter started successfully")
	
	return nil
}

// Stop stops the mainframe adapter service
func (ma *MainframeAdapter) Stop() error {
	ma.mutex.Lock()
	defer ma.mutex.Unlock()
	
	if !ma.running {
		return fmt.Errorf("mainframe adapter is not running")
	}
	
	log.Println("Stopping mainframe adapter...")
	
	// Cancel context to stop all operations
	ma.cancel()
	
	// Stop managers
	ma.monitoringManager.Stop()
	ma.dataManager.Stop()
	ma.jobManager.Stop()
	ma.connectionManager.Stop()
	
	ma.running = false
	log.Println("Mainframe adapter stopped successfully")
	
	return nil
}

// registerProtocolHandlers registers all available protocol handlers
func (ma *MainframeAdapter) registerProtocolHandlers() {
	// Register FTP handler
	ma.protocolHandlers["ftp"] = NewFTPHandler(ma.config.FTPConfig)
	
	// Register SFTP handler
	ma.protocolHandlers["sftp"] = NewSFTPHandler(ma.config.SFTPConfig)
	
	// Register MQ handler
	ma.protocolHandlers["mq"] = NewMQHandler(ma.config.MQConfig)
	
	// Register Telnet handler
	ma.protocolHandlers["telnet"] = NewTelnetHandler(ma.config.TelnetConfig)
	
	// Register TCP socket handler
	ma.protocolHandlers["tcp"] = NewTCPHandler()
	
	log.Printf("Registered %d protocol handlers", len(ma.protocolHandlers))
}

// GetProtocolHandler returns a protocol handler by name
func (ma *MainframeAdapter) GetProtocolHandler(protocol string) (ProtocolHandler, error) {
	handler, exists := ma.protocolHandlers[protocol]
	if !exists {
		return nil, fmt.Errorf("protocol handler not found: %s", protocol)
	}
	return handler, nil
}

// CreateConnection creates a new mainframe connection profile
func (ma *MainframeAdapter) CreateConnection(connection *MainframeConnection) error {
	// Validate connection parameters
	if err := ma.validateConnectionConfig(connection); err != nil {
		return fmt.Errorf("invalid connection configuration: %w", err)
	}
	
	// Encrypt sensitive data
	if err := ma.securityManager.EncryptConnectionCredentials(connection); err != nil {
		return fmt.Errorf("failed to encrypt credentials: %w", err)
	}
	
	// Save to database
	if err := ma.db.DB.Create(connection).Error; err != nil {
		return fmt.Errorf("failed to save connection: %w", err)
	}
	
	log.Printf("Created mainframe connection: %s", connection.Name)
	return nil
}

// TestConnection tests connectivity to a mainframe system
func (ma *MainframeAdapter) TestConnection(connectionID uuid.UUID) error {
	// Load connection
	var connection MainframeConnection
	if err := ma.db.DB.First(&connection, connectionID).Error; err != nil {
		return fmt.Errorf("connection not found: %w", err)
	}
	
	// Decrypt credentials
	if err := ma.securityManager.DecryptConnectionCredentials(&connection); err != nil {
		return fmt.Errorf("failed to decrypt credentials: %w", err)
	}
	
	// Test connection using appropriate protocol
	return ma.connectionManager.TestConnection(&connection)
}

// SubmitJob submits a job to a mainframe system
func (ma *MainframeAdapter) SubmitJob(submission *MainframeJobSubmission) error {
	return ma.jobManager.SubmitJob(submission)
}

// GetJobStatus retrieves the status of a mainframe job
func (ma *MainframeAdapter) GetJobStatus(submissionID uuid.UUID) (*MainframeJobSubmission, error) {
	return ma.jobManager.GetJobStatus(submissionID)
}

// TransferData initiates a data transfer to/from mainframe
func (ma *MainframeAdapter) TransferData(transfer *MainframeDataTransfer) error {
	return ma.dataManager.TransferData(transfer)
}

// ExecuteDatasetOperation performs dataset operations
func (ma *MainframeAdapter) ExecuteDatasetOperation(operation *MainframeDatasetOperation) error {
	return ma.dataManager.ExecuteDatasetOperation(operation)
}

// GetMonitoringMetrics retrieves monitoring metrics from mainframe systems
func (ma *MainframeAdapter) GetMonitoringMetrics(connectionID uuid.UUID, metricTypes []string) ([]*MainframeMonitoringMetric, error) {
	return ma.monitoringManager.GetMetrics(connectionID, metricTypes)
}

// validateConnectionConfig validates mainframe connection configuration
func (ma *MainframeAdapter) validateConnectionConfig(connection *MainframeConnection) error {
	if connection.Name == "" {
		return fmt.Errorf("connection name is required")
	}
	
	if connection.Hostname == "" {
		return fmt.Errorf("hostname is required")
	}
	
	if connection.Username == "" {
		return fmt.Errorf("username is required")
	}
	
	if connection.Port <= 0 || connection.Port > 65535 {
		return fmt.Errorf("invalid port number: %d", connection.Port)
	}
	
	// Validate system type
	validSystemTypes := []string{"zos", "mvs", "vse", "vm"}
	valid := false
	for _, validType := range validSystemTypes {
		if connection.SystemType == validType {
			valid = true
			break
		}
	}
	if !valid {
		return fmt.Errorf("invalid system type: %s", connection.SystemType)
	}
	
	return nil
}

// IsRunning returns whether the adapter is currently running
func (ma *MainframeAdapter) IsRunning() bool {
	ma.mutex.RLock()
	defer ma.mutex.RUnlock()
	return ma.running
}

// GetStats returns adapter statistics
func (ma *MainframeAdapter) GetStats() map[string]interface{} {
	stats := map[string]interface{}{
		"running":              ma.IsRunning(),
		"protocol_handlers":    len(ma.protocolHandlers),
		"active_connections":   ma.connectionManager.GetActiveConnectionCount(),
		"pending_jobs":         ma.jobManager.GetPendingJobCount(),
		"active_transfers":     ma.dataManager.GetActiveTransferCount(),
	}
	
	return stats
}
