package mainframe

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// MainframeConnection represents a connection profile to a mainframe system
type MainframeConnection struct {
	ID                uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	Name              string    `json:"name" gorm:"uniqueIndex;not null"`
	Description       *string   `json:"description"`
	Environment       string    `json:"environment" gorm:"not null"` // development, test, production
	SystemType        string    `json:"system_type" gorm:"not null"` // zos, mvs, vse, vm
	
	// Connection details
	Hostname          string    `json:"hostname" gorm:"not null"`
	Port              int       `json:"port" gorm:"default:23"`
	
	// Authentication
	Username          string    `json:"username" gorm:"not null"`
	PasswordEncrypted string    `json:"-" gorm:"column:password_encrypted"`
	AuthMethod        string    `json:"auth_method" gorm:"default:'password'"` // password, certificate, kerberos
	
	// Security system integration
	SecuritySystem    string    `json:"security_system"` // RACF, ACF2, TopSecret
	SecurityProfile   *string   `json:"security_profile"`
	
	// Connection settings
	Timeout           int       `json:"timeout" gorm:"default:30"`
	MaxConnections    int       `json:"max_connections" gorm:"default:10"`
	KeepAlive         bool      `json:"keep_alive" gorm:"default:true"`
	
	// SSL/TLS settings
	UseSSL            bool      `json:"use_ssl" gorm:"default:false"`
	SSLCertPath       *string   `json:"ssl_cert_path"`
	SSLKeyPath        *string   `json:"ssl_key_path"`
	SSLCAPath         *string   `json:"ssl_ca_path"`
	
	// Protocol-specific settings
	ProtocolSettings  map[string]interface{} `json:"protocol_settings" gorm:"type:jsonb;default:'{}'"`
	
	// Status and monitoring
	Status            string    `json:"status" gorm:"default:'inactive'"` // active, inactive, error
	LastConnected     *time.Time `json:"last_connected"`
	LastError         *string   `json:"last_error"`
	
	// Metadata
	Tags              []string  `json:"tags" gorm:"type:jsonb;default:'[]'"`
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`
	CreatedBy         *uuid.UUID `json:"created_by" gorm:"type:uuid"`
	
	// Associations
	JobSubmissions    []MainframeJobSubmission `json:"job_submissions" gorm:"foreignKey:ConnectionID"`
	DataTransfers     []MainframeDataTransfer  `json:"data_transfers" gorm:"foreignKey:ConnectionID"`
}

// MainframeJobSubmission represents a job submitted to a mainframe system
type MainframeJobSubmission struct {
	ID               uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	ConnectionID     uuid.UUID `json:"connection_id" gorm:"type:uuid;not null"`
	JobInstanceID    *uuid.UUID `json:"job_instance_id" gorm:"type:uuid"` // Link to WorkflowMaster job instance
	
	// Job details
	JobName          string    `json:"job_name" gorm:"not null"`
	JobClass         string    `json:"job_class" gorm:"default:'A'"`
	JobType          string    `json:"job_type" gorm:"not null"` // JCL, REXX, COBOL, etc.
	Priority         int       `json:"priority" gorm:"default:5"`
	
	// JCL and job content
	JCLContent       string    `json:"jcl_content" gorm:"type:text"`
	JobParameters    map[string]interface{} `json:"job_parameters" gorm:"type:jsonb;default:'{}'"`
	
	// Mainframe job tracking
	MainframeJobID   *string   `json:"mainframe_job_id"` // Job ID assigned by mainframe
	MainframeJobNumber *string `json:"mainframe_job_number"` // Job number (JOBxxxxx)
	
	// Status and execution
	Status           string    `json:"status" gorm:"default:'pending'"` // pending, submitted, running, completed, failed, cancelled
	SubmittedAt      *time.Time `json:"submitted_at"`
	StartedAt        *time.Time `json:"started_at"`
	CompletedAt      *time.Time `json:"completed_at"`
	
	// Results
	ReturnCode       *int      `json:"return_code"`
	ConditionCode    *string   `json:"condition_code"`
	AbendCode        *string   `json:"abend_code"`
	
	// Logs and output
	JobLog           *string   `json:"job_log" gorm:"type:text"`
	SysoutData       *string   `json:"sysout_data" gorm:"type:text"`
	ErrorMessage     *string   `json:"error_message"`
	
	// Resource usage
	CPUTime          *float64  `json:"cpu_time"` // CPU seconds used
	ElapsedTime      *float64  `json:"elapsed_time"` // Wall clock time
	MemoryUsed       *int64    `json:"memory_used"` // Memory in KB
	
	// Metadata
	CreatedAt        time.Time `json:"created_at"`
	UpdatedAt        time.Time `json:"updated_at"`
	
	// Associations
	Connection       *MainframeConnection `json:"connection" gorm:"foreignKey:ConnectionID"`
	DatasetOperations []MainframeDatasetOperation `json:"dataset_operations" gorm:"foreignKey:JobSubmissionID"`
}

// MainframeDataTransfer represents file/data transfers to/from mainframe
type MainframeDataTransfer struct {
	ID               uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	ConnectionID     uuid.UUID `json:"connection_id" gorm:"type:uuid;not null"`
	JobInstanceID    *uuid.UUID `json:"job_instance_id" gorm:"type:uuid"`
	
	// Transfer details
	TransferType     string    `json:"transfer_type" gorm:"not null"` // upload, download, copy
	Protocol         string    `json:"protocol" gorm:"not null"` // FTP, SFTP, NFS, TCP
	
	// Source and destination
	SourcePath       string    `json:"source_path" gorm:"not null"`
	DestinationPath  string    `json:"destination_path" gorm:"not null"`
	SourceType       string    `json:"source_type"` // dataset, file, member
	DestinationType  string    `json:"destination_type"` // dataset, file, member
	
	// Dataset information
	DatasetName      *string   `json:"dataset_name"`
	MemberName       *string   `json:"member_name"`
	DatasetType      *string   `json:"dataset_type"` // PS, PO, VSAM, etc.
	RecordFormat     *string   `json:"record_format"` // FB, VB, U
	RecordLength     *int      `json:"record_length"`
	BlockSize        *int      `json:"block_size"`
	
	// Transfer options
	TransferMode     string    `json:"transfer_mode" gorm:"default:'binary'"` // binary, ascii, ebcdic
	ConvertEncoding  bool      `json:"convert_encoding" gorm:"default:false"`
	SourceEncoding   string    `json:"source_encoding" gorm:"default:'EBCDIC'"`
	TargetEncoding   string    `json:"target_encoding" gorm:"default:'UTF-8'"`
	
	// Status and progress
	Status           string    `json:"status" gorm:"default:'pending'"` // pending, transferring, completed, failed
	Progress         float64   `json:"progress" gorm:"default:0"` // Percentage complete
	BytesTransferred int64     `json:"bytes_transferred" gorm:"default:0"`
	TotalBytes       int64     `json:"total_bytes" gorm:"default:0"`
	
	// Timing
	StartedAt        *time.Time `json:"started_at"`
	CompletedAt      *time.Time `json:"completed_at"`
	
	// Error handling
	ErrorMessage     *string   `json:"error_message"`
	RetryCount       int       `json:"retry_count" gorm:"default:0"`
	
	// Metadata
	CreatedAt        time.Time `json:"created_at"`
	UpdatedAt        time.Time `json:"updated_at"`
	
	// Associations
	Connection       *MainframeConnection `json:"connection" gorm:"foreignKey:ConnectionID"`
}

// MainframeDatasetOperation represents dataset operations (allocate, delete, copy, etc.)
type MainframeDatasetOperation struct {
	ID               uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	ConnectionID     uuid.UUID `json:"connection_id" gorm:"type:uuid;not null"`
	JobSubmissionID  *uuid.UUID `json:"job_submission_id" gorm:"type:uuid"`
	
	// Operation details
	Operation        string    `json:"operation" gorm:"not null"` // allocate, delete, copy, rename, catalog, uncatalog
	DatasetName      string    `json:"dataset_name" gorm:"not null"`
	MemberName       *string   `json:"member_name"`
	
	// Dataset attributes
	DatasetType      string    `json:"dataset_type"` // PS, PO, VSAM, etc.
	RecordFormat     string    `json:"record_format"` // FB, VB, U
	RecordLength     int       `json:"record_length"`
	BlockSize        int       `json:"block_size"`
	Space            *string   `json:"space"` // Space allocation (CYL, TRK)
	VolumeSerial     *string   `json:"volume_serial"`
	
	// Copy operation specific
	SourceDataset    *string   `json:"source_dataset"`
	TargetDataset    *string   `json:"target_dataset"`
	
	// Status
	Status           string    `json:"status" gorm:"default:'pending'"` // pending, executing, completed, failed
	ExecutedAt       *time.Time `json:"executed_at"`
	CompletedAt      *time.Time `json:"completed_at"`
	
	// Results
	ReturnCode       *int      `json:"return_code"`
	ErrorMessage     *string   `json:"error_message"`
	
	// Metadata
	CreatedAt        time.Time `json:"created_at"`
	UpdatedAt        time.Time `json:"updated_at"`
	
	// Associations
	Connection       *MainframeConnection `json:"connection" gorm:"foreignKey:ConnectionID"`
	JobSubmission    *MainframeJobSubmission `json:"job_submission" gorm:"foreignKey:JobSubmissionID"`
}

// MainframeMonitoringMetric represents monitoring data from mainframe systems
type MainframeMonitoringMetric struct {
	ID               uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	ConnectionID     uuid.UUID `json:"connection_id" gorm:"type:uuid;not null"`
	
	// Metric details
	MetricType       string    `json:"metric_type" gorm:"not null"` // cpu, memory, disk, job_queue, etc.
	MetricName       string    `json:"metric_name" gorm:"not null"`
	MetricValue      float64   `json:"metric_value"`
	MetricUnit       string    `json:"metric_unit"`
	
	// Context
	SystemName       string    `json:"system_name"`
	LparName         *string   `json:"lpar_name"`
	SubsystemName    *string   `json:"subsystem_name"`
	
	// Additional data
	MetricData       map[string]interface{} `json:"metric_data" gorm:"type:jsonb;default:'{}'"`
	
	// Timing
	CollectedAt      time.Time `json:"collected_at"`
	CreatedAt        time.Time `json:"created_at"`
	
	// Associations
	Connection       *MainframeConnection `json:"connection" gorm:"foreignKey:ConnectionID"`
}

// MainframeCopybook represents COBOL copybook definitions for data parsing
type MainframeCopybook struct {
	ID               uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	Name             string    `json:"name" gorm:"uniqueIndex;not null"`
	Description      *string   `json:"description"`
	
	// Copybook content
	CopybookContent  string    `json:"copybook_content" gorm:"type:text;not null"`
	ParsedStructure  map[string]interface{} `json:"parsed_structure" gorm:"type:jsonb"`
	
	// Metadata
	Version          string    `json:"version" gorm:"default:'1.0'"`
	CreatedAt        time.Time `json:"created_at"`
	UpdatedAt        time.Time `json:"updated_at"`
	CreatedBy        *uuid.UUID `json:"created_by" gorm:"type:uuid"`
}

// BeforeCreate hooks
func (mc *MainframeConnection) BeforeCreate(tx *gorm.DB) error {
	if mc.ID == uuid.Nil {
		mc.ID = uuid.New()
	}
	return nil
}

func (mjs *MainframeJobSubmission) BeforeCreate(tx *gorm.DB) error {
	if mjs.ID == uuid.Nil {
		mjs.ID = uuid.New()
	}
	return nil
}

func (mdt *MainframeDataTransfer) BeforeCreate(tx *gorm.DB) error {
	if mdt.ID == uuid.Nil {
		mdt.ID = uuid.New()
	}
	return nil
}

func (mdo *MainframeDatasetOperation) BeforeCreate(tx *gorm.DB) error {
	if mdo.ID == uuid.Nil {
		mdo.ID = uuid.New()
	}
	return nil
}

func (mmm *MainframeMonitoringMetric) BeforeCreate(tx *gorm.DB) error {
	if mmm.ID == uuid.Nil {
		mmm.ID = uuid.New()
	}
	return nil
}

func (mc *MainframeCopybook) BeforeCreate(tx *gorm.DB) error {
	if mc.ID == uuid.Nil {
		mc.ID = uuid.New()
	}
	return nil
}
