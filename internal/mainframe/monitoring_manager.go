package mainframe

import (
	"context"
	"fmt"
	"log"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/workflowmaster/workflowmaster/internal/database"
)

// MonitoringManager handles mainframe system monitoring
type MonitoringManager struct {
	db                *database.Database
	config            *AdapterConfig
	connectionManager *ConnectionManager
	
	// Monitoring state
	activeMonitors    map[uuid.UUID]*MonitoringSession
	monitorMutex      sync.RWMutex
	
	// Metric collectors
	collectors        map[string]MetricCollector
	
	// Monitoring
	ctx               context.Context
	running           bool
}

// MonitoringSession represents an active monitoring session
type MonitoringSession struct {
	ConnectionID      uuid.UUID
	Connection        Connection
	MetricTypes       []string
	Interval          time.Duration
	LastCollection    time.Time
	ErrorCount        int
	MaxErrors         int
	Status            string
}

// MetricCollector interface for different types of metric collection
type MetricCollector interface {
	CollectMetrics(conn Connection, systemName string) ([]*MainframeMonitoringMetric, error)
	GetMetricType() string
	GetSupportedSystems() []string
}

// SystemResourceCollector collects system resource metrics
type SystemResourceCollector struct {
	metricType string
}

// JobQueueCollector collects job queue metrics
type JobQueueCollector struct {
	metricType string
}

// DatabaseCollector collects database metrics
type DatabaseCollector struct {
	metricType string
}

// NetworkCollector collects network metrics
type NetworkCollector struct {
	metricType string
}

// StorageCollector collects storage metrics
type StorageCollector struct {
	metricType string
}

// MonitoringAlert represents an alert condition
type MonitoringAlert struct {
	ID              uuid.UUID              `json:"id"`
	ConnectionID    uuid.UUID              `json:"connection_id"`
	MetricType      string                 `json:"metric_type"`
	MetricName      string                 `json:"metric_name"`
	Condition       string                 `json:"condition"` // >, <, >=, <=, ==, !=
	Threshold       float64                `json:"threshold"`
	Severity        string                 `json:"severity"` // info, warning, critical
	Enabled         bool                   `json:"enabled"`
	NotificationChannels []string          `json:"notification_channels"`
	Attributes      map[string]interface{} `json:"attributes"`
	CreatedAt       time.Time              `json:"created_at"`
	UpdatedAt       time.Time              `json:"updated_at"`
}

// AlertEvent represents a triggered alert
type AlertEvent struct {
	ID              uuid.UUID              `json:"id"`
	AlertID         uuid.UUID              `json:"alert_id"`
	MetricValue     float64                `json:"metric_value"`
	Threshold       float64                `json:"threshold"`
	Severity        string                 `json:"severity"`
	Message         string                 `json:"message"`
	Acknowledged    bool                   `json:"acknowledged"`
	AcknowledgedBy  *uuid.UUID             `json:"acknowledged_by"`
	AcknowledgedAt  *time.Time             `json:"acknowledged_at"`
	ResolvedAt      *time.Time             `json:"resolved_at"`
	CreatedAt       time.Time              `json:"created_at"`
}

// NewMonitoringManager creates a new monitoring manager
func NewMonitoringManager(db *database.Database, config *AdapterConfig) *MonitoringManager {
	mm := &MonitoringManager{
		db:             db,
		config:         config,
		activeMonitors: make(map[uuid.UUID]*MonitoringSession),
		collectors:     make(map[string]MetricCollector),
		running:        false,
	}
	
	// Register metric collectors
	mm.registerCollectors()
	
	return mm
}

// Start starts the monitoring manager
func (mm *MonitoringManager) Start(ctx context.Context) error {
	mm.ctx = ctx
	mm.running = true
	
	// Start monitoring goroutine
	go mm.monitoringSupervisor()
	
	// Start alert processing
	go mm.alertProcessor()
	
	log.Println("Monitoring manager started")
	return nil
}

// Stop stops the monitoring manager
func (mm *MonitoringManager) Stop() {
	mm.running = false
	
	// Stop all monitoring sessions
	mm.monitorMutex.Lock()
	mm.activeMonitors = make(map[uuid.UUID]*MonitoringSession)
	mm.monitorMutex.Unlock()
	
	log.Println("Monitoring manager stopped")
}

// registerCollectors registers all metric collectors
func (mm *MonitoringManager) registerCollectors() {
	mm.collectors["system_resources"] = &SystemResourceCollector{metricType: "system_resources"}
	mm.collectors["job_queue"] = &JobQueueCollector{metricType: "job_queue"}
	mm.collectors["database"] = &DatabaseCollector{metricType: "database"}
	mm.collectors["network"] = &NetworkCollector{metricType: "network"}
	mm.collectors["storage"] = &StorageCollector{metricType: "storage"}
	
	log.Printf("Registered %d metric collectors", len(mm.collectors))
}

// StartMonitoring starts monitoring for a connection
func (mm *MonitoringManager) StartMonitoring(connectionID uuid.UUID, metricTypes []string) error {
	// Check if already monitoring
	mm.monitorMutex.RLock()
	_, exists := mm.activeMonitors[connectionID]
	mm.monitorMutex.RUnlock()
	
	if exists {
		return fmt.Errorf("monitoring already active for connection: %s", connectionID)
	}
	
	// Get connection
	conn, err := mm.connectionManager.GetConnection(connectionID)
	if err != nil {
		return fmt.Errorf("failed to get connection: %w", err)
	}
	
	// Create monitoring session
	session := &MonitoringSession{
		ConnectionID:   connectionID,
		Connection:     conn,
		MetricTypes:    metricTypes,
		Interval:       mm.config.MonitoringInterval,
		LastCollection: time.Now(),
		ErrorCount:     0,
		MaxErrors:      5,
		Status:         "active",
	}
	
	// Add to active monitors
	mm.monitorMutex.Lock()
	mm.activeMonitors[connectionID] = session
	mm.monitorMutex.Unlock()
	
	log.Printf("Started monitoring for connection: %s", connectionID)
	return nil
}

// StopMonitoring stops monitoring for a connection
func (mm *MonitoringManager) StopMonitoring(connectionID uuid.UUID) error {
	mm.monitorMutex.Lock()
	session, exists := mm.activeMonitors[connectionID]
	if exists {
		delete(mm.activeMonitors, connectionID)
		// Return connection to pool
		mm.connectionManager.ReturnConnection(session.Connection)
	}
	mm.monitorMutex.Unlock()
	
	if !exists {
		return fmt.Errorf("monitoring not active for connection: %s", connectionID)
	}
	
	log.Printf("Stopped monitoring for connection: %s", connectionID)
	return nil
}

// GetMetrics retrieves metrics for a connection
func (mm *MonitoringManager) GetMetrics(connectionID uuid.UUID, metricTypes []string) ([]*MainframeMonitoringMetric, error) {
	// Get connection
	conn, err := mm.connectionManager.GetConnection(connectionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get connection: %w", err)
	}
	defer mm.connectionManager.ReturnConnection(conn)
	
	var allMetrics []*MainframeMonitoringMetric
	
	// Collect metrics for each type
	for _, metricType := range metricTypes {
		collector, exists := mm.collectors[metricType]
		if !exists {
			log.Printf("Unknown metric type: %s", metricType)
			continue
		}
		
		metrics, err := collector.CollectMetrics(conn, "MAINFRAME")
		if err != nil {
			log.Printf("Failed to collect %s metrics: %v", metricType, err)
			continue
		}
		
		// Set connection ID for all metrics
		for _, metric := range metrics {
			metric.ConnectionID = connectionID
			metric.CollectedAt = time.Now()
		}
		
		allMetrics = append(allMetrics, metrics...)
	}
	
	// Save metrics to database
	for _, metric := range allMetrics {
		if err := mm.db.DB.Create(metric).Error; err != nil {
			log.Printf("Failed to save metric: %v", err)
		}
	}
	
	return allMetrics, nil
}

// monitoringSupervisor manages all monitoring sessions
func (mm *MonitoringManager) monitoringSupervisor() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()
	
	for {
		select {
		case <-mm.ctx.Done():
			return
		case <-ticker.C:
			if mm.running {
				mm.processMonitoringSessions()
			}
		}
	}
}

// processMonitoringSessions processes all active monitoring sessions
func (mm *MonitoringManager) processMonitoringSessions() {
	mm.monitorMutex.RLock()
	sessions := make([]*MonitoringSession, 0, len(mm.activeMonitors))
	for _, session := range mm.activeMonitors {
		sessions = append(sessions, session)
	}
	mm.monitorMutex.RUnlock()
	
	for _, session := range sessions {
		if time.Since(session.LastCollection) >= session.Interval {
			go mm.collectSessionMetrics(session)
		}
	}
}

// collectSessionMetrics collects metrics for a monitoring session
func (mm *MonitoringManager) collectSessionMetrics(session *MonitoringSession) {
	metrics, err := mm.GetMetrics(session.ConnectionID, session.MetricTypes)
	if err != nil {
		session.ErrorCount++
		log.Printf("Failed to collect metrics for session %s: %v", session.ConnectionID, err)
		
		if session.ErrorCount >= session.MaxErrors {
			session.Status = "error"
			log.Printf("Monitoring session %s disabled due to errors", session.ConnectionID)
		}
		return
	}
	
	// Reset error count on successful collection
	session.ErrorCount = 0
	session.LastCollection = time.Now()
	
	// Process alerts for collected metrics
	mm.processMetricAlerts(metrics)
}

// processMetricAlerts processes alerts for collected metrics
func (mm *MonitoringManager) processMetricAlerts(metrics []*MainframeMonitoringMetric) {
	for _, metric := range metrics {
		// Load alerts for this metric
		var alerts []MonitoringAlert
		err := mm.db.DB.Where("connection_id = ? AND metric_type = ? AND metric_name = ? AND enabled = ?",
			metric.ConnectionID, metric.MetricType, metric.MetricName, true).Find(&alerts).Error
		if err != nil {
			log.Printf("Failed to load alerts: %v", err)
			continue
		}
		
		// Check each alert condition
		for _, alert := range alerts {
			if mm.evaluateAlertCondition(metric.MetricValue, alert.Condition, alert.Threshold) {
				mm.triggerAlert(&alert, metric)
			}
		}
	}
}

// evaluateAlertCondition evaluates an alert condition
func (mm *MonitoringManager) evaluateAlertCondition(value float64, condition string, threshold float64) bool {
	switch condition {
	case ">":
		return value > threshold
	case "<":
		return value < threshold
	case ">=":
		return value >= threshold
	case "<=":
		return value <= threshold
	case "==":
		return value == threshold
	case "!=":
		return value != threshold
	default:
		return false
	}
}

// triggerAlert triggers an alert
func (mm *MonitoringManager) triggerAlert(alert *MonitoringAlert, metric *MainframeMonitoringMetric) {
	alertEvent := &AlertEvent{
		ID:          uuid.New(),
		AlertID:     alert.ID,
		MetricValue: metric.MetricValue,
		Threshold:   alert.Threshold,
		Severity:    alert.Severity,
		Message: fmt.Sprintf("Alert: %s %s %f (current: %f)",
			metric.MetricName, alert.Condition, alert.Threshold, metric.MetricValue),
		Acknowledged: false,
		CreatedAt:    time.Now(),
	}
	
	// Save alert event
	if err := mm.db.DB.Create(alertEvent).Error; err != nil {
		log.Printf("Failed to save alert event: %v", err)
		return
	}
	
	log.Printf("Alert triggered: %s", alertEvent.Message)
	
	// Send notifications (implementation would depend on notification channels)
	mm.sendAlertNotifications(alert, alertEvent)
}

// sendAlertNotifications sends alert notifications
func (mm *MonitoringManager) sendAlertNotifications(alert *MonitoringAlert, event *AlertEvent) {
	// Implementation would send notifications via configured channels
	// (email, SMS, webhook, etc.)
	log.Printf("Sending alert notifications for: %s", event.Message)
}

// alertProcessor processes alert events
func (mm *MonitoringManager) alertProcessor() {
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()
	
	for {
		select {
		case <-mm.ctx.Done():
			return
		case <-ticker.C:
			if mm.running {
				mm.processAlertEvents()
			}
		}
	}
}

// processAlertEvents processes pending alert events
func (mm *MonitoringManager) processAlertEvents() {
	// Load unacknowledged alerts
	var events []AlertEvent
	err := mm.db.DB.Where("acknowledged = ? AND resolved_at IS NULL", false).
		Order("created_at DESC").
		Limit(100).
		Find(&events).Error
	if err != nil {
		log.Printf("Failed to load alert events: %v", err)
		return
	}
	
	// Process each event (auto-resolution, escalation, etc.)
	for _, event := range events {
		mm.processAlertEvent(&event)
	}
}

// processAlertEvent processes a single alert event
func (mm *MonitoringManager) processAlertEvent(event *AlertEvent) {
	// Auto-resolve alerts after a certain time if conditions are met
	if time.Since(event.CreatedAt) > 1*time.Hour && !event.Acknowledged {
		// Check if the condition is still true
		// If not, auto-resolve the alert
		now := time.Now()
		event.ResolvedAt = &now
		mm.db.DB.Save(event)
		log.Printf("Auto-resolved alert: %s", event.Message)
	}
}

// System Resource Collector Implementation

func (src *SystemResourceCollector) CollectMetrics(conn Connection, systemName string) ([]*MainframeMonitoringMetric, error) {
	var metrics []*MainframeMonitoringMetric
	
	// Collect CPU metrics
	cpuOutput, err := conn.ExecuteCommand("D A,L")
	if err == nil {
		cpuMetrics := src.parseCPUMetrics(cpuOutput, systemName)
		metrics = append(metrics, cpuMetrics...)
	}
	
	// Collect memory metrics
	memOutput, err := conn.ExecuteCommand("D M")
	if err == nil {
		memMetrics := src.parseMemoryMetrics(memOutput, systemName)
		metrics = append(metrics, memMetrics...)
	}
	
	return metrics, nil
}

func (src *SystemResourceCollector) GetMetricType() string {
	return src.metricType
}

func (src *SystemResourceCollector) GetSupportedSystems() []string {
	return []string{"zos", "mvs"}
}

func (src *SystemResourceCollector) parseCPUMetrics(output, systemName string) []*MainframeMonitoringMetric {
	var metrics []*MainframeMonitoringMetric
	
	// Parse CPU utilization from output
	lines := strings.Split(output, "\n")
	for _, line := range lines {
		if strings.Contains(line, "CPU") && strings.Contains(line, "%") {
			// Extract CPU percentage
			re := regexp.MustCompile(`(\d+\.?\d*)%`)
			matches := re.FindStringSubmatch(line)
			if len(matches) > 1 {
				if value, err := strconv.ParseFloat(matches[1], 64); err == nil {
					metric := &MainframeMonitoringMetric{
						ID:         uuid.New(),
						MetricType: "system_resources",
						MetricName: "cpu_utilization",
						MetricValue: value,
						MetricUnit: "percent",
						SystemName: systemName,
						MetricData: map[string]interface{}{
							"raw_output": line,
						},
					}
					metrics = append(metrics, metric)
				}
			}
		}
	}
	
	return metrics
}

func (src *SystemResourceCollector) parseMemoryMetrics(output, systemName string) []*MainframeMonitoringMetric {
	var metrics []*MainframeMonitoringMetric
	
	// Parse memory usage from output
	lines := strings.Split(output, "\n")
	for _, line := range lines {
		if strings.Contains(line, "STORAGE") || strings.Contains(line, "MEMORY") {
			// Extract memory values
			re := regexp.MustCompile(`(\d+)K`)
			matches := re.FindAllStringSubmatch(line, -1)
			for _, match := range matches {
				if len(match) > 1 {
					if value, err := strconv.ParseFloat(match[1], 64); err == nil {
						metric := &MainframeMonitoringMetric{
							ID:         uuid.New(),
							MetricType: "system_resources",
							MetricName: "memory_usage",
							MetricValue: value * 1024, // Convert to bytes
							MetricUnit: "bytes",
							SystemName: systemName,
							MetricData: map[string]interface{}{
								"raw_output": line,
							},
						}
						metrics = append(metrics, metric)
					}
				}
			}
		}
	}
	
	return metrics
}

// Job Queue Collector Implementation

func (jqc *JobQueueCollector) CollectMetrics(conn Connection, systemName string) ([]*MainframeMonitoringMetric, error) {
	var metrics []*MainframeMonitoringMetric
	
	// Collect job queue metrics
	output, err := conn.ExecuteCommand("$DJQ")
	if err != nil {
		return nil, fmt.Errorf("failed to get job queue status: %w", err)
	}
	
	metrics = jqc.parseJobQueueMetrics(output, systemName)
	return metrics, nil
}

func (jqc *JobQueueCollector) GetMetricType() string {
	return jqc.metricType
}

func (jqc *JobQueueCollector) GetSupportedSystems() []string {
	return []string{"zos", "mvs"}
}

func (jqc *JobQueueCollector) parseJobQueueMetrics(output, systemName string) []*MainframeMonitoringMetric {
	var metrics []*MainframeMonitoringMetric
	
	// Count jobs in different states
	lines := strings.Split(output, "\n")
	jobCounts := map[string]int{
		"waiting":   0,
		"executing": 0,
		"output":    0,
	}
	
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.Contains(line, "WAITING") {
			jobCounts["waiting"]++
		} else if strings.Contains(line, "EXECUTING") {
			jobCounts["executing"]++
		} else if strings.Contains(line, "OUTPUT") {
			jobCounts["output"]++
		}
	}
	
	// Create metrics for each job state
	for state, count := range jobCounts {
		metric := &MainframeMonitoringMetric{
			ID:         uuid.New(),
			MetricType: "job_queue",
			MetricName: fmt.Sprintf("jobs_%s", state),
			MetricValue: float64(count),
			MetricUnit: "count",
			SystemName: systemName,
			MetricData: map[string]interface{}{
				"job_state": state,
			},
		}
		metrics = append(metrics, metric)
	}
	
	return metrics
}

// Placeholder implementations for other collectors
func (dc *DatabaseCollector) CollectMetrics(conn Connection, systemName string) ([]*MainframeMonitoringMetric, error) {
	return []*MainframeMonitoringMetric{}, nil
}

func (dc *DatabaseCollector) GetMetricType() string {
	return dc.metricType
}

func (dc *DatabaseCollector) GetSupportedSystems() []string {
	return []string{"zos", "mvs"}
}

func (nc *NetworkCollector) CollectMetrics(conn Connection, systemName string) ([]*MainframeMonitoringMetric, error) {
	return []*MainframeMonitoringMetric{}, nil
}

func (nc *NetworkCollector) GetMetricType() string {
	return nc.metricType
}

func (nc *NetworkCollector) GetSupportedSystems() []string {
	return []string{"zos", "mvs"}
}

func (sc *StorageCollector) CollectMetrics(conn Connection, systemName string) ([]*MainframeMonitoringMetric, error) {
	return []*MainframeMonitoringMetric{}, nil
}

func (sc *StorageCollector) GetMetricType() string {
	return sc.metricType
}

func (sc *StorageCollector) GetSupportedSystems() []string {
	return []string{"zos", "mvs"}
}
