package protocols

import (
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/ibm-messaging/mq-golang/v5/ibmmq"
	"github.com/workflowmaster/workflowmaster/internal/mainframe"
)

// MQHandler implements IBM MQ protocol for mainframe communication
type MQHandler struct {
	config *mainframe.MQConfig
}

// MQConnection represents an active MQ connection
type MQConnection struct {
	id           uuid.UUID
	connection   *mainframe.MainframeConnection
	queueManager ibmmq.MQQueueManager
	queues       map[string]ibmmq.MQObject
	lastActivity time.Time
	connected    bool
}

// MQMessage represents an MQ message
type MQMessage struct {
	MessageID     string
	CorrelationID string
	MessageType   int32
	Priority      int32
	Persistence   int32
	Expiry        int32
	Format        string
	Data          []byte
	Properties    map[string]interface{}
}

// NewMQHandler creates a new MQ protocol handler
func NewMQHandler(config *mainframe.MQConfig) *MQHandler {
	if config == nil {
		config = &mainframe.MQConfig{
			QueueManager: "QM1",
			Channel:      "SYSTEM.DEF.SVRCONN",
			ConnName:     "localhost(1414)",
			UserID:       "",
			Timeout:      30 * time.Second,
		}
	}
	
	return &MQHandler{
		config: config,
	}
}

// Connect establishes an MQ connection to the mainframe
func (h *MQHandler) Connect(connection *mainframe.MainframeConnection) (mainframe.Connection, error) {
	// Create connection descriptor
	cd := ibmmq.NewMQCD()
	cd.ChannelName = h.config.Channel
	cd.ConnectionName = h.config.ConnName
	cd.ChannelType = ibmmq.MQCHT_CLNTCONN
	
	// Set SSL options if enabled
	if connection.UseSSL {
		cd.SSLCipherSpec = "TLS_RSA_WITH_AES_256_CBC_SHA256"
		if connection.SSLCertPath != nil {
			cd.CertLabel = *connection.SSLCertPath
		}
	}
	
	// Create connection security parameters
	csp := ibmmq.NewMQCSP()
	csp.AuthenticationType = ibmmq.MQCSP_AUTH_USER_ID_AND_PWD
	csp.UserId = connection.Username
	csp.Password = connection.PasswordEncrypted // Note: should be decrypted
	
	// Create connection options
	cno := ibmmq.NewMQCNO()
	cno.ClientConn = cd
	cno.SecurityParms = csp
	cno.Options = ibmmq.MQCNO_CLIENT_BINDING
	
	// Connect to queue manager
	qMgr, err := ibmmq.Connx(h.config.QueueManager, cno)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to MQ queue manager: %w", err)
	}
	
	// Create connection wrapper
	mqConn := &MQConnection{
		id:           uuid.New(),
		connection:   connection,
		queueManager: qMgr,
		queues:       make(map[string]ibmmq.MQObject),
		lastActivity: time.Now(),
		connected:    true,
	}
	
	return mqConn, nil
}

// Disconnect closes the MQ connection
func (h *MQHandler) Disconnect(conn mainframe.Connection) error {
	mqConn, ok := conn.(*MQConnection)
	if !ok {
		return fmt.Errorf("invalid connection type")
	}
	
	// Close all open queues
	for _, queue := range mqConn.queues {
		queue.Close(0)
	}
	
	// Disconnect from queue manager
	if mqConn.connected {
		mqConn.queueManager.Disc()
	}
	mqConn.connected = false
	
	return nil
}

// GetProtocolName returns the protocol name
func (h *MQHandler) GetProtocolName() string {
	return "mq"
}

// ValidateConnection validates MQ connection parameters
func (h *MQHandler) ValidateConnection(connection *mainframe.MainframeConnection) error {
	if connection.Hostname == "" {
		return fmt.Errorf("hostname is required for MQ connection")
	}
	
	if connection.Port <= 0 {
		connection.Port = 1414 // Default MQ port
	}
	
	if connection.Username == "" {
		return fmt.Errorf("username is required for MQ connection")
	}
	
	return nil
}

// MQConnection methods implementing the Connection interface

// IsConnected returns whether the connection is active
func (c *MQConnection) IsConnected() bool {
	if !c.connected {
		return false
	}
	
	// Test connection by inquiring queue manager
	_, err := c.queueManager.Inq(ibmmq.MQIA_Q_MGR_STATUS)
	if err != nil {
		c.connected = false
		return false
	}
	
	c.lastActivity = time.Now()
	return true
}

// GetConnectionID returns the connection ID
func (c *MQConnection) GetConnectionID() uuid.UUID {
	return c.id
}

// GetLastActivity returns the last activity time
func (c *MQConnection) GetLastActivity() time.Time {
	return c.lastActivity
}

// Close closes the MQ connection
func (c *MQConnection) Close() error {
	// Close all open queues
	for _, queue := range c.queues {
		queue.Close(0)
	}
	c.queues = make(map[string]ibmmq.MQObject)
	
	// Disconnect from queue manager
	if c.connected {
		err := c.queueManager.Disc()
		c.connected = false
		return err
	}
	
	return nil
}

// ExecuteCommand executes an MQ command
func (c *MQConnection) ExecuteCommand(command string) (string, error) {
	if !c.IsConnected() {
		return "", fmt.Errorf("connection not active")
	}
	
	c.lastActivity = time.Now()
	
	// Parse command
	parts := strings.Fields(command)
	if len(parts) == 0 {
		return "", fmt.Errorf("empty command")
	}
	
	cmd := strings.ToUpper(parts[0])
	
	switch cmd {
	case "PUT":
		if len(parts) < 3 {
			return "", fmt.Errorf("usage: PUT <queue_name> <message>")
		}
		queueName := parts[1]
		message := strings.Join(parts[2:], " ")
		return c.putMessage(queueName, message)
		
	case "GET":
		if len(parts) < 2 {
			return "", fmt.Errorf("usage: GET <queue_name> [timeout]")
		}
		queueName := parts[1]
		timeout := 5000 // Default 5 seconds
		if len(parts) > 2 {
			if t, err := time.ParseDuration(parts[2]); err == nil {
				timeout = int(t.Milliseconds())
			}
		}
		return c.getMessage(queueName, timeout)
		
	case "BROWSE":
		if len(parts) < 2 {
			return "", fmt.Errorf("usage: BROWSE <queue_name>")
		}
		queueName := parts[1]
		return c.browseQueue(queueName)
		
	case "DEPTH":
		if len(parts) < 2 {
			return "", fmt.Errorf("usage: DEPTH <queue_name>")
		}
		queueName := parts[1]
		return c.getQueueDepth(queueName)
		
	case "CLEAR":
		if len(parts) < 2 {
			return "", fmt.Errorf("usage: CLEAR <queue_name>")
		}
		queueName := parts[1]
		return c.clearQueue(queueName)
		
	case "STATUS":
		return c.getQueueManagerStatus()
		
	default:
		return "", fmt.Errorf("unsupported MQ command: %s", cmd)
	}
}

// putMessage puts a message to a queue
func (c *MQConnection) putMessage(queueName, message string) (string, error) {
	// Open queue for output
	queue, err := c.openQueue(queueName, ibmmq.MQOO_OUTPUT)
	if err != nil {
		return "", fmt.Errorf("failed to open queue for output: %w", err)
	}
	
	// Create message descriptor
	md := ibmmq.NewMQMD()
	md.Format = ibmmq.MQFMT_STRING
	md.MessageType = ibmmq.MQMT_DATAGRAM
	md.Persistence = ibmmq.MQPER_PERSISTENT
	
	// Create put message options
	pmo := ibmmq.NewMQPMO()
	pmo.Options = ibmmq.MQPMO_NO_SYNCPOINT
	
	// Put message
	err = queue.Put(md, pmo, []byte(message))
	if err != nil {
		return "", fmt.Errorf("failed to put message: %w", err)
	}
	
	return fmt.Sprintf("Message put to queue %s (MessageID: %x)", queueName, md.MessageId), nil
}

// getMessage gets a message from a queue
func (c *MQConnection) getMessage(queueName string, timeout int) (string, error) {
	// Open queue for input
	queue, err := c.openQueue(queueName, ibmmq.MQOO_INPUT_SHARED)
	if err != nil {
		return "", fmt.Errorf("failed to open queue for input: %w", err)
	}
	
	// Create message descriptor
	md := ibmmq.NewMQMD()
	
	// Create get message options
	gmo := ibmmq.NewMQGMO()
	gmo.Options = ibmmq.MQGMO_NO_SYNCPOINT | ibmmq.MQGMO_WAIT
	gmo.WaitInterval = int32(timeout)
	
	// Get message
	buffer := make([]byte, 1024*1024) // 1MB buffer
	datalen, err := queue.Get(md, gmo, buffer)
	if err != nil {
		if err.(*ibmmq.MQReturn).MQRC == ibmmq.MQRC_NO_MSG_AVAILABLE {
			return "No message available", nil
		}
		return "", fmt.Errorf("failed to get message: %w", err)
	}
	
	message := string(buffer[:datalen])
	return fmt.Sprintf("Message from queue %s (MessageID: %x): %s", 
		queueName, md.MessageId, message), nil
}

// browseQueue browses messages in a queue without removing them
func (c *MQConnection) browseQueue(queueName string) (string, error) {
	// Open queue for browse
	queue, err := c.openQueue(queueName, ibmmq.MQOO_BROWSE)
	if err != nil {
		return "", fmt.Errorf("failed to open queue for browse: %w", err)
	}
	
	var result strings.Builder
	result.WriteString(fmt.Sprintf("Browsing queue %s:\n", queueName))
	
	// Browse messages
	md := ibmmq.NewMQMD()
	gmo := ibmmq.NewMQGMO()
	gmo.Options = ibmmq.MQGMO_BROWSE_FIRST | ibmmq.MQGMO_NO_WAIT
	
	buffer := make([]byte, 1024*1024)
	messageCount := 0
	
	for {
		datalen, err := queue.Get(md, gmo, buffer)
		if err != nil {
			if err.(*ibmmq.MQReturn).MQRC == ibmmq.MQRC_NO_MSG_AVAILABLE {
				break
			}
			return "", fmt.Errorf("failed to browse message: %w", err)
		}
		
		messageCount++
		message := string(buffer[:datalen])
		result.WriteString(fmt.Sprintf("Message %d (ID: %x): %s\n", 
			messageCount, md.MessageId, message))
		
		// Switch to browse next
		gmo.Options = ibmmq.MQGMO_BROWSE_NEXT | ibmmq.MQGMO_NO_WAIT
		md = ibmmq.NewMQMD() // Reset message descriptor
	}
	
	result.WriteString(fmt.Sprintf("Total messages: %d", messageCount))
	return result.String(), nil
}

// getQueueDepth gets the current depth of a queue
func (c *MQConnection) getQueueDepth(queueName string) (string, error) {
	// Open queue for inquire
	queue, err := c.openQueue(queueName, ibmmq.MQOO_INQUIRE)
	if err != nil {
		return "", fmt.Errorf("failed to open queue for inquire: %w", err)
	}
	
	// Inquire queue depth
	depth, err := queue.Inq(ibmmq.MQIA_CURRENT_Q_DEPTH)
	if err != nil {
		return "", fmt.Errorf("failed to inquire queue depth: %w", err)
	}
	
	return fmt.Sprintf("Queue %s depth: %d", queueName, depth[0]), nil
}

// clearQueue clears all messages from a queue
func (c *MQConnection) clearQueue(queueName string) (string, error) {
	// Open queue for input
	queue, err := c.openQueue(queueName, ibmmq.MQOO_INPUT_EXCLUSIVE)
	if err != nil {
		return "", fmt.Errorf("failed to open queue for clear: %w", err)
	}
	
	// Get all messages without waiting
	md := ibmmq.NewMQMD()
	gmo := ibmmq.NewMQGMO()
	gmo.Options = ibmmq.MQGMO_NO_SYNCPOINT | ibmmq.MQGMO_NO_WAIT
	
	buffer := make([]byte, 1024*1024)
	messageCount := 0
	
	for {
		_, err := queue.Get(md, gmo, buffer)
		if err != nil {
			if err.(*ibmmq.MQReturn).MQRC == ibmmq.MQRC_NO_MSG_AVAILABLE {
				break
			}
			return "", fmt.Errorf("failed to clear message: %w", err)
		}
		messageCount++
		md = ibmmq.NewMQMD() // Reset message descriptor
	}
	
	return fmt.Sprintf("Cleared %d messages from queue %s", messageCount, queueName), nil
}

// getQueueManagerStatus gets the status of the queue manager
func (c *MQConnection) getQueueManagerStatus() (string, error) {
	status, err := c.queueManager.Inq(ibmmq.MQIA_Q_MGR_STATUS)
	if err != nil {
		return "", fmt.Errorf("failed to get queue manager status: %w", err)
	}
	
	var statusText string
	switch status[0] {
	case ibmmq.MQQMSTA_RUNNING:
		statusText = "RUNNING"
	case ibmmq.MQQMSTA_QUIESCING:
		statusText = "QUIESCING"
	case ibmmq.MQQMSTA_STARTING:
		statusText = "STARTING"
	case ibmmq.MQQMSTA_STOPPING:
		statusText = "STOPPING"
	default:
		statusText = "UNKNOWN"
	}
	
	return fmt.Sprintf("Queue Manager Status: %s", statusText), nil
}

// openQueue opens a queue with the specified options
func (c *MQConnection) openQueue(queueName string, options int32) (ibmmq.MQObject, error) {
	// Check if queue is already open
	if queue, exists := c.queues[queueName]; exists {
		return queue, nil
	}
	
	// Create object descriptor
	od := ibmmq.NewMQOD()
	od.ObjectType = ibmmq.MQOT_Q
	od.ObjectName = queueName
	
	// Open queue
	queue, err := c.queueManager.Open(od, options)
	if err != nil {
		return ibmmq.MQObject{}, err
	}
	
	// Cache the queue
	c.queues[queueName] = queue
	
	return queue, nil
}

// TransferFile is not applicable for MQ protocol
func (c *MQConnection) TransferFile(source, destination string, options mainframe.TransferOptions) error {
	return fmt.Errorf("file transfer not supported for MQ protocol")
}
