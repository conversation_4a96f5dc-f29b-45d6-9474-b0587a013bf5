package protocols

import (
	"fmt"
	"io"
	"net"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/sftp"
	"golang.org/x/crypto/ssh"
	"github.com/workflowmaster/workflowmaster/internal/mainframe"
)

// SFTPHandler implements SFTP protocol for mainframe communication
type SFTPHandler struct {
	config *mainframe.SFTPConfig
}

// SFTPConnection represents an active SFTP connection
type SFTPConnection struct {
	id           uuid.UUID
	connection   *mainframe.MainframeConnection
	sshClient    *ssh.Client
	sftpClient   *sftp.Client
	lastActivity time.Time
	connected    bool
}

// NewSFTPHandler creates a new SFTP protocol handler
func NewSFTPHandler(config *mainframe.SFTPConfig) *SFTPHandler {
	if config == nil {
		config = &mainframe.SFTPConfig{
			KeyExchanges: []string{"diffie-hellman-group14-sha256", "diffie-hellman-group14-sha1"},
			Ciphers:      []string{"aes128-ctr", "aes192-ctr", "aes256-ctr"},
			MACs:         []string{"hmac-sha2-256", "hmac-sha1"},
			Timeout:      30 * time.Second,
		}
	}
	
	return &SFTPHandler{
		config: config,
	}
}

// Connect establishes an SFTP connection to the mainframe
func (h *SFTPHandler) Connect(connection *mainframe.MainframeConnection) (mainframe.Connection, error) {
	// Create SSH client configuration
	sshConfig := &ssh.ClientConfig{
		User:    connection.Username,
		Timeout: h.config.Timeout,
		HostKeyCallback: ssh.InsecureIgnoreHostKey(), // In production, use proper host key verification
	}
	
	// Set authentication method
	if connection.AuthMethod == "password" {
		sshConfig.Auth = []ssh.AuthMethod{
			ssh.Password(connection.PasswordEncrypted), // Note: should be decrypted
		}
	} else if connection.AuthMethod == "key" {
		// Load private key
		key, err := h.loadPrivateKey(connection.SSLKeyPath)
		if err != nil {
			return nil, fmt.Errorf("failed to load private key: %w", err)
		}
		sshConfig.Auth = []ssh.AuthMethod{ssh.PublicKeys(key)}
	}
	
	// Configure crypto algorithms
	if len(h.config.KeyExchanges) > 0 {
		sshConfig.KeyExchanges = h.config.KeyExchanges
	}
	if len(h.config.Ciphers) > 0 {
		sshConfig.Ciphers = h.config.Ciphers
	}
	if len(h.config.MACs) > 0 {
		sshConfig.MACs = h.config.MACs
	}
	
	// Connect to SSH server
	addr := fmt.Sprintf("%s:%d", connection.Hostname, connection.Port)
	sshClient, err := ssh.Dial("tcp", addr, sshConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to SSH server: %w", err)
	}
	
	// Create SFTP client
	sftpClient, err := sftp.NewClient(sshClient)
	if err != nil {
		sshClient.Close()
		return nil, fmt.Errorf("failed to create SFTP client: %w", err)
	}
	
	// Create connection wrapper
	sftpConn := &SFTPConnection{
		id:           uuid.New(),
		connection:   connection,
		sshClient:    sshClient,
		sftpClient:   sftpClient,
		lastActivity: time.Now(),
		connected:    true,
	}
	
	return sftpConn, nil
}

// Disconnect closes the SFTP connection
func (h *SFTPHandler) Disconnect(conn mainframe.Connection) error {
	sftpConn, ok := conn.(*SFTPConnection)
	if !ok {
		return fmt.Errorf("invalid connection type")
	}
	
	if sftpConn.sftpClient != nil {
		sftpConn.sftpClient.Close()
	}
	if sftpConn.sshClient != nil {
		sftpConn.sshClient.Close()
	}
	sftpConn.connected = false
	
	return nil
}

// GetProtocolName returns the protocol name
func (h *SFTPHandler) GetProtocolName() string {
	return "sftp"
}

// ValidateConnection validates SFTP connection parameters
func (h *SFTPHandler) ValidateConnection(connection *mainframe.MainframeConnection) error {
	if connection.Hostname == "" {
		return fmt.Errorf("hostname is required for SFTP connection")
	}
	
	if connection.Port <= 0 {
		connection.Port = 22 // Default SSH port
	}
	
	if connection.Username == "" {
		return fmt.Errorf("username is required for SFTP connection")
	}
	
	if connection.AuthMethod == "key" && connection.SSLKeyPath == nil {
		return fmt.Errorf("private key path is required for key authentication")
	}
	
	return nil
}

// loadPrivateKey loads a private key for SSH authentication
func (h *SFTPHandler) loadPrivateKey(keyPath *string) (ssh.Signer, error) {
	if keyPath == nil {
		return nil, fmt.Errorf("key path is nil")
	}
	
	keyData, err := os.ReadFile(*keyPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read private key: %w", err)
	}
	
	key, err := ssh.ParsePrivateKey(keyData)
	if err != nil {
		return nil, fmt.Errorf("failed to parse private key: %w", err)
	}
	
	return key, nil
}

// SFTPConnection methods implementing the Connection interface

// IsConnected returns whether the connection is active
func (c *SFTPConnection) IsConnected() bool {
	if !c.connected || c.sshClient == nil || c.sftpClient == nil {
		return false
	}
	
	// Test connection by getting working directory
	_, err := c.sftpClient.Getwd()
	if err != nil {
		c.connected = false
		return false
	}
	
	c.lastActivity = time.Now()
	return true
}

// GetConnectionID returns the connection ID
func (c *SFTPConnection) GetConnectionID() uuid.UUID {
	return c.id
}

// GetLastActivity returns the last activity time
func (c *SFTPConnection) GetLastActivity() time.Time {
	return c.lastActivity
}

// Close closes the SFTP connection
func (c *SFTPConnection) Close() error {
	var err error
	if c.sftpClient != nil {
		err = c.sftpClient.Close()
		c.sftpClient = nil
	}
	if c.sshClient != nil {
		sshErr := c.sshClient.Close()
		if err == nil {
			err = sshErr
		}
		c.sshClient = nil
	}
	c.connected = false
	return err
}

// ExecuteCommand executes an SFTP command or SSH command
func (c *SFTPConnection) ExecuteCommand(command string) (string, error) {
	if !c.IsConnected() {
		return "", fmt.Errorf("connection not active")
	}
	
	c.lastActivity = time.Now()
	
	// Parse command
	parts := strings.Fields(command)
	if len(parts) == 0 {
		return "", fmt.Errorf("empty command")
	}
	
	cmd := strings.ToUpper(parts[0])
	
	switch cmd {
	case "PWD":
		dir, err := c.sftpClient.Getwd()
		return dir, err
		
	case "CWD", "CD":
		if len(parts) < 2 {
			return "", fmt.Errorf("directory path required")
		}
		err := c.sftpClient.Chdir(parts[1])
		if err != nil {
			return "", err
		}
		return "Directory changed", nil
		
	case "LIST", "LS":
		var path string
		if len(parts) > 1 {
			path = parts[1]
		} else {
			path = "."
		}
		
		files, err := c.sftpClient.ReadDir(path)
		if err != nil {
			return "", err
		}
		
		var result strings.Builder
		for _, file := range files {
			mode := file.Mode().String()
			size := file.Size()
			modTime := file.ModTime().Format("Jan 02 15:04")
			name := file.Name()
			
			result.WriteString(fmt.Sprintf("%s %10d %s %s\n", mode, size, modTime, name))
		}
		return result.String(), nil
		
	case "MKDIR", "MKD":
		if len(parts) < 2 {
			return "", fmt.Errorf("directory name required")
		}
		err := c.sftpClient.Mkdir(parts[1])
		if err != nil {
			return "", err
		}
		return "Directory created", nil
		
	case "RMDIR", "RMD":
		if len(parts) < 2 {
			return "", fmt.Errorf("directory name required")
		}
		err := c.sftpClient.RemoveDirectory(parts[1])
		if err != nil {
			return "", err
		}
		return "Directory removed", nil
		
	case "DELETE", "DELE", "RM":
		if len(parts) < 2 {
			return "", fmt.Errorf("file name required")
		}
		err := c.sftpClient.Remove(parts[1])
		if err != nil {
			return "", err
		}
		return "File deleted", nil
		
	case "RENAME", "MV":
		if len(parts) < 3 {
			return "", fmt.Errorf("source and destination names required")
		}
		err := c.sftpClient.Rename(parts[1], parts[2])
		if err != nil {
			return "", err
		}
		return "File renamed", nil
		
	case "STAT":
		if len(parts) < 2 {
			return "", fmt.Errorf("file name required")
		}
		info, err := c.sftpClient.Stat(parts[1])
		if err != nil {
			return "", err
		}
		return fmt.Sprintf("%s %d %s", info.Mode(), info.Size(), info.ModTime()), nil
		
	default:
		// For other commands, try to execute via SSH
		return c.executeSSHCommand(command)
	}
}

// executeSSHCommand executes a command via SSH
func (c *SFTPConnection) executeSSHCommand(command string) (string, error) {
	session, err := c.sshClient.NewSession()
	if err != nil {
		return "", fmt.Errorf("failed to create SSH session: %w", err)
	}
	defer session.Close()
	
	output, err := session.CombinedOutput(command)
	if err != nil {
		return "", fmt.Errorf("command execution failed: %w", err)
	}
	
	return string(output), nil
}

// TransferFile transfers a file using SFTP
func (c *SFTPConnection) TransferFile(source, destination string, options mainframe.TransferOptions) error {
	if !c.IsConnected() {
		return fmt.Errorf("connection not active")
	}
	
	c.lastActivity = time.Now()
	
	// Determine transfer direction
	if strings.HasPrefix(source, "local:") && !strings.HasPrefix(destination, "local:") {
		// Upload: local to mainframe
		return c.uploadFile(strings.TrimPrefix(source, "local:"), destination, options)
	} else if !strings.HasPrefix(source, "local:") && strings.HasPrefix(destination, "local:") {
		// Download: mainframe to local
		return c.downloadFile(source, strings.TrimPrefix(destination, "local:"), options)
	} else {
		return fmt.Errorf("invalid transfer specification")
	}
}

// uploadFile uploads a file to the mainframe
func (c *SFTPConnection) uploadFile(localPath, remotePath string, options mainframe.TransferOptions) error {
	// Open local file
	localFile, err := os.Open(localPath)
	if err != nil {
		return fmt.Errorf("failed to open local file: %w", err)
	}
	defer localFile.Close()
	
	// Create remote file
	remoteFile, err := c.sftpClient.Create(remotePath)
	if err != nil {
		return fmt.Errorf("failed to create remote file: %w", err)
	}
	defer remoteFile.Close()
	
	// Copy data
	_, err = io.Copy(remoteFile, localFile)
	if err != nil {
		return fmt.Errorf("failed to copy file data: %w", err)
	}
	
	// Set permissions if specified
	if options.PreserveAttrs {
		localInfo, err := localFile.Stat()
		if err == nil {
			c.sftpClient.Chmod(remotePath, localInfo.Mode())
		}
	}
	
	return nil
}

// downloadFile downloads a file from the mainframe
func (c *SFTPConnection) downloadFile(remotePath, localPath string, options mainframe.TransferOptions) error {
	// Open remote file
	remoteFile, err := c.sftpClient.Open(remotePath)
	if err != nil {
		return fmt.Errorf("failed to open remote file: %w", err)
	}
	defer remoteFile.Close()
	
	// Create local directory if needed
	if options.CreatePath {
		localDir := filepath.Dir(localPath)
		if err := os.MkdirAll(localDir, 0755); err != nil {
			return fmt.Errorf("failed to create local directory: %w", err)
		}
	}
	
	// Create local file
	localFile, err := os.Create(localPath)
	if err != nil {
		return fmt.Errorf("failed to create local file: %w", err)
	}
	defer localFile.Close()
	
	// Copy data
	_, err = io.Copy(localFile, remoteFile)
	if err != nil {
		return fmt.Errorf("failed to copy file data: %w", err)
	}
	
	// Preserve attributes if specified
	if options.PreserveAttrs {
		remoteInfo, err := remoteFile.Stat()
		if err == nil {
			os.Chmod(localPath, remoteInfo.Mode())
			os.Chtimes(localPath, remoteInfo.ModTime(), remoteInfo.ModTime())
		}
	}
	
	return nil
}

// GetFileInfo retrieves information about a file
func (c *SFTPConnection) GetFileInfo(path string) (*FileInfo, error) {
	if !c.IsConnected() {
		return nil, fmt.Errorf("connection not active")
	}
	
	c.lastActivity = time.Now()
	
	info, err := c.sftpClient.Stat(path)
	if err != nil {
		return nil, fmt.Errorf("failed to get file info: %w", err)
	}
	
	return &FileInfo{
		Name:         info.Name(),
		Size:         uint64(info.Size()),
		ModTime:      info.ModTime(),
		IsDirectory:  info.IsDir(),
		Permissions:  info.Mode().String(),
	}, nil
}
