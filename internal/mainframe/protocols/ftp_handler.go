package protocols

import (
	"fmt"
	"io"
	"net"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/jlaffaye/ftp"
	"github.com/workflowmaster/workflowmaster/internal/mainframe"
)

// FTPHandler implements FTP protocol for mainframe communication
type F<PERSON><PERSON><PERSON>ler struct {
	config *mainframe.FTPConfig
}

// FTPConnection represents an active FTP connection
type FTPConnection struct {
	id           uuid.UUID
	connection   *mainframe.MainframeConnection
	client       *ftp.ServerConn
	lastActivity time.Time
	connected    bool
}

// NewFTPHandler creates a new FTP protocol handler
func NewFTPHandler(config *mainframe.FTPConfig) *FTPHandler {
	if config == nil {
		config = &mainframe.FTPConfig{
			PassiveMode: true,
			BinaryMode:  true,
			Timeout:     30 * time.Second,
			KeepAlive:   60 * time.Second,
		}
	}
	
	return &FTPHandler{
		config: config,
	}
}

// Connect establishes an FTP connection to the mainframe
func (h *FTPHandler) Connect(connection *mainframe.MainframeConnection) (mainframe.Connection, error) {
	// Create connection string
	addr := fmt.Sprintf("%s:%d", connection.Hostname, connection.Port)
	
	// Set up dialer with timeout
	dialer := &net.Dialer{
		Timeout: h.config.Timeout,
	}
	
	// Connect to FTP server
	client, err := ftp.Dial(addr, ftp.DialWithTimeout(h.config.Timeout))
	if err != nil {
		return nil, fmt.Errorf("failed to connect to FTP server: %w", err)
	}
	
	// Login with credentials
	err = client.Login(connection.Username, connection.PasswordEncrypted) // Note: should be decrypted
	if err != nil {
		client.Quit()
		return nil, fmt.Errorf("FTP login failed: %w", err)
	}
	
	// Set transfer mode
	if h.config.BinaryMode {
		err = client.Type(ftp.TransferTypeBinary)
	} else {
		err = client.Type(ftp.TransferTypeASCII)
	}
	if err != nil {
		client.Quit()
		return nil, fmt.Errorf("failed to set transfer mode: %w", err)
	}
	
	// Create connection wrapper
	ftpConn := &FTPConnection{
		id:           uuid.New(),
		connection:   connection,
		client:       client,
		lastActivity: time.Now(),
		connected:    true,
	}
	
	return ftpConn, nil
}

// Disconnect closes the FTP connection
func (h *FTPHandler) Disconnect(conn mainframe.Connection) error {
	ftpConn, ok := conn.(*FTPConnection)
	if !ok {
		return fmt.Errorf("invalid connection type")
	}
	
	if ftpConn.client != nil {
		ftpConn.client.Quit()
	}
	ftpConn.connected = false
	
	return nil
}

// GetProtocolName returns the protocol name
func (h *FTPHandler) GetProtocolName() string {
	return "ftp"
}

// ValidateConnection validates FTP connection parameters
func (h *FTPHandler) ValidateConnection(connection *mainframe.MainframeConnection) error {
	if connection.Hostname == "" {
		return fmt.Errorf("hostname is required for FTP connection")
	}
	
	if connection.Port <= 0 {
		connection.Port = 21 // Default FTP port
	}
	
	if connection.Username == "" {
		return fmt.Errorf("username is required for FTP connection")
	}
	
	return nil
}

// FTPConnection methods implementing the Connection interface

// IsConnected returns whether the connection is active
func (c *FTPConnection) IsConnected() bool {
	if !c.connected || c.client == nil {
		return false
	}
	
	// Test connection with NOOP command
	err := c.client.NoOp()
	if err != nil {
		c.connected = false
		return false
	}
	
	c.lastActivity = time.Now()
	return true
}

// GetConnectionID returns the connection ID
func (c *FTPConnection) GetConnectionID() uuid.UUID {
	return c.id
}

// GetLastActivity returns the last activity time
func (c *FTPConnection) GetLastActivity() time.Time {
	return c.lastActivity
}

// Close closes the FTP connection
func (c *FTPConnection) Close() error {
	if c.client != nil {
		err := c.client.Quit()
		c.client = nil
		c.connected = false
		return err
	}
	return nil
}

// ExecuteCommand executes an FTP command
func (c *FTPConnection) ExecuteCommand(command string) (string, error) {
	if !c.IsConnected() {
		return "", fmt.Errorf("connection not active")
	}
	
	c.lastActivity = time.Now()
	
	// Parse command
	parts := strings.Fields(command)
	if len(parts) == 0 {
		return "", fmt.Errorf("empty command")
	}
	
	cmd := strings.ToUpper(parts[0])
	
	switch cmd {
	case "PWD":
		dir, err := c.client.CurrentDir()
		return dir, err
		
	case "CWD", "CD":
		if len(parts) < 2 {
			return "", fmt.Errorf("directory path required")
		}
		err := c.client.ChangeDir(parts[1])
		if err != nil {
			return "", err
		}
		return "Directory changed", nil
		
	case "LIST", "LS":
		var path string
		if len(parts) > 1 {
			path = parts[1]
		} else {
			path = "."
		}
		
		entries, err := c.client.List(path)
		if err != nil {
			return "", err
		}
		
		var result strings.Builder
		for _, entry := range entries {
			result.WriteString(fmt.Sprintf("%s %10d %s %s\n",
				entry.Type, entry.Size, entry.Time.Format("Jan 02 15:04"), entry.Name))
		}
		return result.String(), nil
		
	case "MKDIR", "MKD":
		if len(parts) < 2 {
			return "", fmt.Errorf("directory name required")
		}
		err := c.client.MakeDir(parts[1])
		if err != nil {
			return "", err
		}
		return "Directory created", nil
		
	case "RMDIR", "RMD":
		if len(parts) < 2 {
			return "", fmt.Errorf("directory name required")
		}
		err := c.client.RemoveDir(parts[1])
		if err != nil {
			return "", err
		}
		return "Directory removed", nil
		
	case "DELETE", "DELE":
		if len(parts) < 2 {
			return "", fmt.Errorf("file name required")
		}
		err := c.client.Delete(parts[1])
		if err != nil {
			return "", err
		}
		return "File deleted", nil
		
	case "RENAME":
		if len(parts) < 3 {
			return "", fmt.Errorf("source and destination names required")
		}
		err := c.client.Rename(parts[1], parts[2])
		if err != nil {
			return "", err
		}
		return "File renamed", nil
		
	case "SIZE":
		if len(parts) < 2 {
			return "", fmt.Errorf("file name required")
		}
		size, err := c.client.FileSize(parts[1])
		if err != nil {
			return "", err
		}
		return fmt.Sprintf("%d", size), nil
		
	case "NOOP":
		err := c.client.NoOp()
		if err != nil {
			return "", err
		}
		return "OK", nil
		
	default:
		return "", fmt.Errorf("unsupported FTP command: %s", cmd)
	}
}

// TransferFile transfers a file using FTP
func (c *FTPConnection) TransferFile(source, destination string, options mainframe.TransferOptions) error {
	if !c.IsConnected() {
		return fmt.Errorf("connection not active")
	}
	
	c.lastActivity = time.Now()
	
	// Determine transfer direction
	if strings.HasPrefix(source, "local:") && !strings.HasPrefix(destination, "local:") {
		// Upload: local to mainframe
		return c.uploadFile(strings.TrimPrefix(source, "local:"), destination, options)
	} else if !strings.HasPrefix(source, "local:") && strings.HasPrefix(destination, "local:") {
		// Download: mainframe to local
		return c.downloadFile(source, strings.TrimPrefix(destination, "local:"), options)
	} else {
		return fmt.Errorf("invalid transfer specification")
	}
}

// uploadFile uploads a file to the mainframe
func (c *FTPConnection) uploadFile(localPath, remotePath string, options mainframe.TransferOptions) error {
	// Open local file
	localFile, err := os.Open(localPath)
	if err != nil {
		return fmt.Errorf("failed to open local file: %w", err)
	}
	defer localFile.Close()
	
	// Set transfer mode based on options
	if options.TransferMode == "binary" {
		err = c.client.Type(ftp.TransferTypeBinary)
	} else {
		err = c.client.Type(ftp.TransferTypeASCII)
	}
	if err != nil {
		return fmt.Errorf("failed to set transfer mode: %w", err)
	}
	
	// Upload file
	err = c.client.Stor(remotePath, localFile)
	if err != nil {
		return fmt.Errorf("failed to upload file: %w", err)
	}
	
	return nil
}

// downloadFile downloads a file from the mainframe
func (c *FTPConnection) downloadFile(remotePath, localPath string, options mainframe.TransferOptions) error {
	// Set transfer mode based on options
	if options.TransferMode == "binary" {
		err := c.client.Type(ftp.TransferTypeBinary)
		if err != nil {
			return fmt.Errorf("failed to set transfer mode: %w", err)
		}
	} else {
		err := c.client.Type(ftp.TransferTypeASCII)
		if err != nil {
			return fmt.Errorf("failed to set transfer mode: %w", err)
		}
	}
	
	// Get file from mainframe
	response, err := c.client.Retr(remotePath)
	if err != nil {
		return fmt.Errorf("failed to retrieve file: %w", err)
	}
	defer response.Close()
	
	// Create local file
	localFile, err := os.Create(localPath)
	if err != nil {
		return fmt.Errorf("failed to create local file: %w", err)
	}
	defer localFile.Close()
	
	// Copy data
	_, err = io.Copy(localFile, response)
	if err != nil {
		return fmt.Errorf("failed to copy file data: %w", err)
	}
	
	return nil
}

// GetFileInfo retrieves information about a file
func (c *FTPConnection) GetFileInfo(path string) (*FileInfo, error) {
	if !c.IsConnected() {
		return nil, fmt.Errorf("connection not active")
	}
	
	c.lastActivity = time.Now()
	
	// Get file size
	size, err := c.client.FileSize(path)
	if err != nil {
		return nil, fmt.Errorf("failed to get file size: %w", err)
	}
	
	// Get directory listing to find file details
	dir := filepath.Dir(path)
	filename := filepath.Base(path)
	
	entries, err := c.client.List(dir)
	if err != nil {
		return nil, fmt.Errorf("failed to list directory: %w", err)
	}
	
	for _, entry := range entries {
		if entry.Name == filename {
			return &FileInfo{
				Name:         entry.Name,
				Size:         entry.Size,
				ModTime:      entry.Time,
				IsDirectory:  entry.Type == ftp.EntryTypeFolder,
				Permissions:  "", // FTP doesn't provide detailed permissions
			}, nil
		}
	}
	
	return nil, fmt.Errorf("file not found: %s", path)
}

// FileInfo represents file information
type FileInfo struct {
	Name         string
	Size         uint64
	ModTime      time.Time
	IsDirectory  bool
	Permissions  string
}

// Additional imports needed
import (
	"os"
	"path/filepath"
)
