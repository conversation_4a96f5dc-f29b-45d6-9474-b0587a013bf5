package mainframe

import (
	"context"
	"fmt"
	"log"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/workflowmaster/workflowmaster/internal/database"
)

// JobManager handles mainframe job submission and monitoring
type JobManager struct {
	db                *database.Database
	config            *AdapterConfig
	connectionManager *ConnectionManager
	
	// Job tracking
	activeJobs        map[uuid.UUID]*JobTracker
	jobMutex          sync.RWMutex
	
	// Monitoring
	ctx               context.Context
	running           bool
}

// JobTracker tracks the status of a submitted job
type JobTracker struct {
	Submission        *MainframeJobSubmission
	Connection        Connection
	LastChecked       time.Time
	CheckInterval     time.Duration
	MaxRetries        int
	RetryCount        int
}

// JCLTemplate represents a reusable JCL template
type JCLTemplate struct {
	ID          uuid.UUID              `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Template    string                 `json:"template"`
	Parameters  map[string]interface{} `json:"parameters"`
	SystemType  string                 `json:"system_type"` // zos, mvs, vse
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// JobSchedulerIntegration handles integration with mainframe job schedulers
type JobSchedulerIntegration struct {
	SchedulerType string                 `json:"scheduler_type"` // ca7, tws, controlm
	Config        map[string]interface{} `json:"config"`
	Enabled       bool                   `json:"enabled"`
}

// NewJobManager creates a new job manager
func NewJobManager(db *database.Database, config *AdapterConfig) *JobManager {
	return &JobManager{
		db:         db,
		config:     config,
		activeJobs: make(map[uuid.UUID]*JobTracker),
		running:    false,
	}
}

// Start starts the job manager
func (jm *JobManager) Start(ctx context.Context) error {
	jm.ctx = ctx
	jm.running = true
	
	// Start job monitoring goroutine
	go jm.monitorJobs()
	
	// Resume monitoring of pending jobs
	if err := jm.resumePendingJobs(); err != nil {
		log.Printf("Warning: failed to resume pending jobs: %v", err)
	}
	
	log.Println("Job manager started")
	return nil
}

// Stop stops the job manager
func (jm *JobManager) Stop() {
	jm.running = false
	
	// Clear active jobs
	jm.jobMutex.Lock()
	jm.activeJobs = make(map[uuid.UUID]*JobTracker)
	jm.jobMutex.Unlock()
	
	log.Println("Job manager stopped")
}

// SubmitJob submits a job to a mainframe system
func (jm *JobManager) SubmitJob(submission *MainframeJobSubmission) error {
	// Validate submission
	if err := jm.validateJobSubmission(submission); err != nil {
		return fmt.Errorf("invalid job submission: %w", err)
	}
	
	// Get connection
	conn, err := jm.connectionManager.GetConnection(submission.ConnectionID)
	if err != nil {
		return fmt.Errorf("failed to get connection: %w", err)
	}
	
	// Process JCL template if needed
	jclContent, err := jm.processJCLTemplate(submission)
	if err != nil {
		return fmt.Errorf("failed to process JCL: %w", err)
	}
	submission.JCLContent = jclContent
	
	// Submit job to mainframe
	jobID, jobNumber, err := jm.submitJobToMainframe(conn, submission)
	if err != nil {
		jm.connectionManager.ReturnConnection(conn)
		return fmt.Errorf("failed to submit job: %w", err)
	}
	
	// Update submission with mainframe job details
	submission.MainframeJobID = &jobID
	submission.MainframeJobNumber = &jobNumber
	submission.Status = "submitted"
	now := time.Now()
	submission.SubmittedAt = &now
	
	// Save to database
	if err := jm.db.DB.Save(submission).Error; err != nil {
		jm.connectionManager.ReturnConnection(conn)
		return fmt.Errorf("failed to save submission: %w", err)
	}
	
	// Start tracking the job
	tracker := &JobTracker{
		Submission:    submission,
		Connection:    conn,
		LastChecked:   time.Now(),
		CheckInterval: jm.config.JobPollingInterval,
		MaxRetries:    jm.config.RetryAttempts,
		RetryCount:    0,
	}
	
	jm.jobMutex.Lock()
	jm.activeJobs[submission.ID] = tracker
	jm.jobMutex.Unlock()
	
	log.Printf("Submitted job %s to mainframe (Job ID: %s)", submission.JobName, jobID)
	return nil
}

// GetJobStatus retrieves the current status of a job
func (jm *JobManager) GetJobStatus(submissionID uuid.UUID) (*MainframeJobSubmission, error) {
	// Check if job is being actively tracked
	jm.jobMutex.RLock()
	tracker, isActive := jm.activeJobs[submissionID]
	jm.jobMutex.RUnlock()
	
	if isActive {
		// Update status from mainframe
		if err := jm.updateJobStatus(tracker); err != nil {
			log.Printf("Failed to update job status: %v", err)
		}
		return tracker.Submission, nil
	}
	
	// Load from database
	var submission MainframeJobSubmission
	if err := jm.db.DB.Preload("Connection").First(&submission, submissionID).Error; err != nil {
		return nil, fmt.Errorf("job submission not found: %w", err)
	}
	
	return &submission, nil
}

// CancelJob cancels a running job
func (jm *JobManager) CancelJob(submissionID uuid.UUID) error {
	jm.jobMutex.RLock()
	tracker, exists := jm.activeJobs[submissionID]
	jm.jobMutex.RUnlock()
	
	if !exists {
		return fmt.Errorf("job not found or not active")
	}
	
	// Cancel job on mainframe
	if err := jm.cancelJobOnMainframe(tracker); err != nil {
		return fmt.Errorf("failed to cancel job: %w", err)
	}
	
	// Update status
	tracker.Submission.Status = "cancelled"
	now := time.Now()
	tracker.Submission.CompletedAt = &now
	
	// Save to database
	if err := jm.db.DB.Save(tracker.Submission).Error; err != nil {
		log.Printf("Failed to save cancelled job status: %v", err)
	}
	
	// Remove from active tracking
	jm.jobMutex.Lock()
	delete(jm.activeJobs, submissionID)
	jm.jobMutex.Unlock()
	
	// Return connection to pool
	jm.connectionManager.ReturnConnection(tracker.Connection)
	
	log.Printf("Cancelled job %s", tracker.Submission.JobName)
	return nil
}

// GetJobLog retrieves the job log from the mainframe
func (jm *JobManager) GetJobLog(submissionID uuid.UUID) (string, error) {
	var submission MainframeJobSubmission
	if err := jm.db.DB.Preload("Connection").First(&submission, submissionID).Error; err != nil {
		return "", fmt.Errorf("job submission not found: %w", err)
	}
	
	if submission.MainframeJobID == nil {
		return "", fmt.Errorf("job not submitted to mainframe")
	}
	
	// Get connection
	conn, err := jm.connectionManager.GetConnection(submission.ConnectionID)
	if err != nil {
		return "", fmt.Errorf("failed to get connection: %w", err)
	}
	defer jm.connectionManager.ReturnConnection(conn)
	
	// Retrieve job log
	jobLog, err := jm.retrieveJobLog(conn, *submission.MainframeJobID)
	if err != nil {
		return "", fmt.Errorf("failed to retrieve job log: %w", err)
	}
	
	// Update submission with log
	submission.JobLog = &jobLog
	jm.db.DB.Save(&submission)
	
	return jobLog, nil
}

// submitJobToMainframe submits a job to the mainframe system
func (jm *JobManager) submitJobToMainframe(conn Connection, submission *MainframeJobSubmission) (string, string, error) {
	// Create temporary dataset for JCL
	datasetName := fmt.Sprintf("%s.JCL.D%s", submission.JobName, time.Now().Format("20060102"))
	
	// Submit JCL using appropriate method based on system type
	var command string
	switch submission.Connection.SystemType {
	case "zos":
		command = fmt.Sprintf("SUBMIT '%s'", datasetName)
	case "mvs":
		command = fmt.Sprintf("SUB JCL(%s)", datasetName)
	default:
		return "", "", fmt.Errorf("unsupported system type: %s", submission.Connection.SystemType)
	}
	
	// Execute submit command
	output, err := conn.ExecuteCommand(command)
	if err != nil {
		return "", "", fmt.Errorf("failed to execute submit command: %w", err)
	}
	
	// Parse job ID and job number from output
	jobID, jobNumber, err := jm.parseJobSubmissionOutput(output)
	if err != nil {
		return "", "", fmt.Errorf("failed to parse job submission output: %w", err)
	}
	
	return jobID, jobNumber, nil
}

// parseJobSubmissionOutput parses the output from job submission to extract job ID and number
func (jm *JobManager) parseJobSubmissionOutput(output string) (string, string, error) {
	// Common patterns for job submission output
	patterns := []string{
		`JOB\s+(\w+)\s+SUBMITTED\s+AS\s+JOB\s+(\w+)`,           // z/OS pattern
		`(\w+)\s+SUBMITTED\s+JOB\s+(\w+)`,                      // MVS pattern
		`JOB\s+(\w+)\((\w+)\)\s+SUBMITTED`,                     // Alternative pattern
	}
	
	for _, pattern := range patterns {
		re := regexp.MustCompile(pattern)
		matches := re.FindStringSubmatch(output)
		if len(matches) >= 3 {
			return matches[1], matches[2], nil
		}
	}
	
	return "", "", fmt.Errorf("could not parse job submission output: %s", output)
}

// updateJobStatus updates the status of a tracked job
func (jm *JobManager) updateJobStatus(tracker *JobTracker) error {
	if tracker.Submission.MainframeJobID == nil {
		return fmt.Errorf("job ID not available")
	}
	
	// Query job status
	command := fmt.Sprintf("ST %s", *tracker.Submission.MainframeJobID)
	output, err := tracker.Connection.ExecuteCommand(command)
	if err != nil {
		tracker.RetryCount++
		if tracker.RetryCount >= tracker.MaxRetries {
			tracker.Submission.Status = "failed"
			tracker.Submission.ErrorMessage = stringPtr(fmt.Sprintf("Failed to check status: %v", err))
			return err
		}
		return nil // Will retry later
	}
	
	// Parse status from output
	status, returnCode, err := jm.parseJobStatus(output)
	if err != nil {
		return fmt.Errorf("failed to parse job status: %w", err)
	}
	
	// Update submission
	oldStatus := tracker.Submission.Status
	tracker.Submission.Status = status
	tracker.LastChecked = time.Now()
	
	if status == "running" && oldStatus != "running" {
		now := time.Now()
		tracker.Submission.StartedAt = &now
	}
	
	if status == "completed" || status == "failed" {
		now := time.Now()
		tracker.Submission.CompletedAt = &now
		tracker.Submission.ReturnCode = returnCode
		
		// Retrieve job log
		if jobLog, err := jm.retrieveJobLog(tracker.Connection, *tracker.Submission.MainframeJobID); err == nil {
			tracker.Submission.JobLog = &jobLog
		}
		
		// Remove from active tracking
		jm.jobMutex.Lock()
		delete(jm.activeJobs, tracker.Submission.ID)
		jm.jobMutex.Unlock()
		
		// Return connection to pool
		jm.connectionManager.ReturnConnection(tracker.Connection)
	}
	
	// Save to database
	return jm.db.DB.Save(tracker.Submission).Error
}

// parseJobStatus parses job status output
func (jm *JobManager) parseJobStatus(output string) (string, *int, error) {
	lines := strings.Split(output, "\n")
	
	for _, line := range lines {
		line = strings.TrimSpace(line)
		
		// Look for status indicators
		if strings.Contains(line, "EXECUTING") || strings.Contains(line, "RUNNING") {
			return "running", nil, nil
		}
		
		if strings.Contains(line, "ENDED") || strings.Contains(line, "COMPLETED") {
			// Try to extract return code
			re := regexp.MustCompile(`RC=(\d+)`)
			matches := re.FindStringSubmatch(line)
			if len(matches) >= 2 {
				if rc, err := strconv.Atoi(matches[1]); err == nil {
					if rc == 0 {
						return "completed", &rc, nil
					} else {
						return "failed", &rc, nil
					}
				}
			}
			return "completed", nil, nil
		}
		
		if strings.Contains(line, "ABENDED") || strings.Contains(line, "FAILED") {
			return "failed", nil, nil
		}
		
		if strings.Contains(line, "CANCELLED") {
			return "cancelled", nil, nil
		}
	}
	
	// Default to pending if no clear status found
	return "pending", nil, nil
}

// retrieveJobLog retrieves the job log from the mainframe
func (jm *JobManager) retrieveJobLog(conn Connection, jobID string) (string, error) {
	command := fmt.Sprintf("OUTPUT %s", jobID)
	output, err := conn.ExecuteCommand(command)
	if err != nil {
		return "", fmt.Errorf("failed to retrieve job log: %w", err)
	}
	
	return output, nil
}

// cancelJobOnMainframe cancels a job on the mainframe
func (jm *JobManager) cancelJobOnMainframe(tracker *JobTracker) error {
	if tracker.Submission.MainframeJobID == nil {
		return fmt.Errorf("job ID not available")
	}
	
	command := fmt.Sprintf("CANCEL %s", *tracker.Submission.MainframeJobID)
	_, err := tracker.Connection.ExecuteCommand(command)
	return err
}

// processJCLTemplate processes JCL templates with parameter substitution
func (jm *JobManager) processJCLTemplate(submission *MainframeJobSubmission) (string, error) {
	jcl := submission.JCLContent
	
	// Substitute parameters
	for key, value := range submission.JobParameters {
		placeholder := fmt.Sprintf("${%s}", key)
		jcl = strings.ReplaceAll(jcl, placeholder, fmt.Sprintf("%v", value))
	}
	
	// Add job card if not present
	if !strings.Contains(jcl, "//") {
		jobCard := fmt.Sprintf("//%s JOB CLASS=%s,MSGCLASS=X\n", 
			submission.JobName, submission.JobClass)
		jcl = jobCard + jcl
	}
	
	return jcl, nil
}

// validateJobSubmission validates a job submission
func (jm *JobManager) validateJobSubmission(submission *MainframeJobSubmission) error {
	if submission.JobName == "" {
		return fmt.Errorf("job name is required")
	}
	
	if submission.JCLContent == "" {
		return fmt.Errorf("JCL content is required")
	}
	
	if submission.ConnectionID == uuid.Nil {
		return fmt.Errorf("connection ID is required")
	}
	
	return nil
}

// monitorJobs monitors active jobs
func (jm *JobManager) monitorJobs() {
	ticker := time.NewTicker(jm.config.JobPollingInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-jm.ctx.Done():
			return
		case <-ticker.C:
			if jm.running {
				jm.checkActiveJobs()
			}
		}
	}
}

// checkActiveJobs checks the status of all active jobs
func (jm *JobManager) checkActiveJobs() {
	jm.jobMutex.RLock()
	trackers := make([]*JobTracker, 0, len(jm.activeJobs))
	for _, tracker := range jm.activeJobs {
		trackers = append(trackers, tracker)
	}
	jm.jobMutex.RUnlock()
	
	for _, tracker := range trackers {
		if time.Since(tracker.LastChecked) >= tracker.CheckInterval {
			if err := jm.updateJobStatus(tracker); err != nil {
				log.Printf("Failed to update job status for %s: %v", 
					tracker.Submission.JobName, err)
			}
		}
	}
}

// resumePendingJobs resumes monitoring of pending jobs after restart
func (jm *JobManager) resumePendingJobs() error {
	var submissions []MainframeJobSubmission
	err := jm.db.DB.Where("status IN (?)", []string{"submitted", "running"}).
		Preload("Connection").
		Find(&submissions).Error
	if err != nil {
		return err
	}
	
	for _, submission := range submissions {
		// Get connection
		conn, err := jm.connectionManager.GetConnection(submission.ConnectionID)
		if err != nil {
			log.Printf("Failed to get connection for job %s: %v", submission.JobName, err)
			continue
		}
		
		// Create tracker
		tracker := &JobTracker{
			Submission:    &submission,
			Connection:    conn,
			LastChecked:   time.Now(),
			CheckInterval: jm.config.JobPollingInterval,
			MaxRetries:    jm.config.RetryAttempts,
			RetryCount:    0,
		}
		
		jm.jobMutex.Lock()
		jm.activeJobs[submission.ID] = tracker
		jm.jobMutex.Unlock()
		
		log.Printf("Resumed monitoring job %s", submission.JobName)
	}
	
	return nil
}

// GetPendingJobCount returns the number of pending jobs
func (jm *JobManager) GetPendingJobCount() int {
	jm.jobMutex.RLock()
	defer jm.jobMutex.RUnlock()
	return len(jm.activeJobs)
}

// Helper function
func stringPtr(s string) *string {
	return &s
}
