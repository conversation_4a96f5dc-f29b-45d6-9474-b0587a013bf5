package mainframe

import (
	"context"
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/workflowmaster/workflowmaster/internal/database"
	"github.com/workflowmaster/workflowmaster/internal/models"
)

// JobExecutor handles execution of mainframe jobs within the WorkflowMaster framework
type JobExecutor struct {
	db      *database.Database
	adapter *MainframeAdapter
}

// ExecutionContext holds context for job execution
type ExecutionContext struct {
	JobInstance      *models.JobInstance
	JobDefinition    *models.JobDefinition
	WorkflowInstance *models.WorkflowInstance
	Parameters       map[string]interface{}
	Environment      map[string]string
	Connection       Connection
}

// ExecutionResult holds the result of job execution
type ExecutionResult struct {
	Success          bool
	ExitCode         int
	Output           string
	ErrorMessage     string
	ResourceUsage    map[string]interface{}
	MainframeJobID   string
	MainframeJobNumber string
	ExecutionTime    time.Duration
	Metrics          map[string]interface{}
}

// NewJobExecutor creates a new mainframe job executor
func NewJobExecutor(db *database.Database, adapter *MainframeAdapter) *JobExecutor {
	return &JobExecutor{
		db:      db,
		adapter: adapter,
	}
}

// ExecuteJob executes a mainframe job
func (je *JobExecutor) ExecuteJob(ctx context.Context, jobInstance *models.JobInstance) (*ExecutionResult, error) {
	// Load job definition
	var jobDefinition models.JobDefinition
	if err := je.db.DB.First(&jobDefinition, jobInstance.JobDefinitionID).Error; err != nil {
		return nil, fmt.Errorf("failed to load job definition: %w", err)
	}

	// Validate that this is a mainframe job
	if !je.isMainframeJob(&jobDefinition) {
		return nil, fmt.Errorf("job is not a mainframe job: %s", jobDefinition.JobType)
	}

	// Create execution context
	execCtx, err := je.createExecutionContext(jobInstance, &jobDefinition)
	if err != nil {
		return nil, fmt.Errorf("failed to create execution context: %w", err)
	}
	defer je.cleanupExecutionContext(execCtx)

	// Execute based on job type
	switch jobDefinition.JobType {
	case models.JobTypeMainframeJCL:
		return je.executeJCLJob(ctx, execCtx)
	case models.JobTypeMainframeREXX:
		return je.executeREXXJob(ctx, execCtx)
	case models.JobTypeMainframeCOBOL:
		return je.executeCOBOLJob(ctx, execCtx)
	case models.JobTypeMainframeDB2:
		return je.executeDB2Job(ctx, execCtx)
	case models.JobTypeMainframeIMS:
		return je.executeIMSJob(ctx, execCtx)
	case models.JobTypeMainframeVSAM:
		return je.executeVSAMJob(ctx, execCtx)
	case models.JobTypeMainframeCICS:
		return je.executeCICSJob(ctx, execCtx)
	case models.JobTypeMainframeMQ:
		return je.executeMQJob(ctx, execCtx)
	default:
		return nil, fmt.Errorf("unsupported mainframe job type: %s", jobDefinition.JobType)
	}
}

// isMainframeJob checks if a job is a mainframe job
func (je *JobExecutor) isMainframeJob(jobDef *models.JobDefinition) bool {
	mainframeJobTypes := []string{
		models.JobTypeMainframeJCL,
		models.JobTypeMainframeREXX,
		models.JobTypeMainframeCOBOL,
		models.JobTypeMainframeDB2,
		models.JobTypeMainframeIMS,
		models.JobTypeMainframeVSAM,
		models.JobTypeMainframeCICS,
		models.JobTypeMainframeMQ,
	}

	for _, jobType := range mainframeJobTypes {
		if jobDef.JobType == jobType {
			return true
		}
	}

	return false
}

// createExecutionContext creates an execution context for the job
func (je *JobExecutor) createExecutionContext(jobInstance *models.JobInstance, jobDef *models.JobDefinition) (*ExecutionContext, error) {
	// Validate mainframe configuration
	if jobDef.MainframeConfig == nil {
		return nil, fmt.Errorf("mainframe configuration is required for mainframe jobs")
	}

	// Get mainframe connection
	conn, err := je.adapter.connectionManager.GetConnection(jobDef.MainframeConfig.ConnectionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get mainframe connection: %w", err)
	}

	// Load workflow instance if applicable
	var workflowInstance *models.WorkflowInstance
	if jobInstance.WorkflowInstanceID != nil {
		workflowInstance = &models.WorkflowInstance{}
		if err := je.db.DB.First(workflowInstance, *jobInstance.WorkflowInstanceID).Error; err != nil {
			log.Printf("Warning: failed to load workflow instance: %v", err)
		}
	}

	// Merge parameters from various sources
	parameters := make(map[string]interface{})
	
	// Start with job definition parameters
	for k, v := range jobDef.Parameters {
		parameters[k] = v
	}
	
	// Override with job instance parameters
	for k, v := range jobInstance.Parameters {
		parameters[k] = v
	}
	
	// Add workflow parameters if available
	if workflowInstance != nil {
		for k, v := range workflowInstance.Parameters {
			if _, exists := parameters[k]; !exists {
				parameters[k] = v
			}
		}
	}

	// Merge environment variables
	environment := make(map[string]string)
	
	// Start with job definition environment
	for k, v := range jobDef.Environment {
		environment[k] = v
	}
	
	// Override with job instance environment
	for k, v := range jobInstance.EnvironmentVariables {
		environment[k] = v
	}

	return &ExecutionContext{
		JobInstance:      jobInstance,
		JobDefinition:    jobDef,
		WorkflowInstance: workflowInstance,
		Parameters:       parameters,
		Environment:      environment,
		Connection:       conn,
	}, nil
}

// cleanupExecutionContext cleans up resources used by the execution context
func (je *JobExecutor) cleanupExecutionContext(ctx *ExecutionContext) {
	if ctx.Connection != nil {
		je.adapter.connectionManager.ReturnConnection(ctx.Connection)
	}
}

// executeJCLJob executes a JCL job
func (je *JobExecutor) executeJCLJob(ctx context.Context, execCtx *ExecutionContext) (*ExecutionResult, error) {
	startTime := time.Now()
	
	// Prepare JCL content
	jclContent, err := je.prepareJCLContent(execCtx)
	if err != nil {
		return nil, fmt.Errorf("failed to prepare JCL content: %w", err)
	}

	// Create job submission
	submission := &MainframeJobSubmission{
		ID:            uuid.New(),
		ConnectionID:  execCtx.JobDefinition.MainframeConfig.ConnectionID,
		JobInstanceID: &execCtx.JobInstance.ID,
		JobName:       execCtx.JobDefinition.Name,
		JobClass:      execCtx.JobDefinition.MainframeConfig.JobClass,
		JobType:       models.MainframeJobTypeJCL,
		JCLContent:    jclContent,
		JobParameters: execCtx.JobDefinition.MainframeConfig.JCLParameters,
		Priority:      execCtx.JobDefinition.Priority,
	}

	// Submit job
	if err := je.adapter.SubmitJob(submission); err != nil {
		return nil, fmt.Errorf("failed to submit JCL job: %w", err)
	}

	// Monitor job execution
	result, err := je.monitorJobExecution(ctx, submission)
	if err != nil {
		return nil, fmt.Errorf("job monitoring failed: %w", err)
	}

	// Calculate execution time
	result.ExecutionTime = time.Since(startTime)

	// Perform post-execution tasks
	if err := je.performPostExecutionTasks(execCtx, result); err != nil {
		log.Printf("Warning: post-execution tasks failed: %v", err)
	}

	return result, nil
}

// executeREXXJob executes a REXX job
func (je *JobExecutor) executeREXXJob(ctx context.Context, execCtx *ExecutionContext) (*ExecutionResult, error) {
	// Similar to JCL but with REXX-specific handling
	startTime := time.Now()
	
	// Prepare REXX script
	rexxScript, err := je.prepareREXXScript(execCtx)
	if err != nil {
		return nil, fmt.Errorf("failed to prepare REXX script: %w", err)
	}

	// Execute REXX script directly
	output, err := execCtx.Connection.ExecuteCommand(rexxScript)
	
	result := &ExecutionResult{
		Success:       err == nil,
		Output:        output,
		ExecutionTime: time.Since(startTime),
		Metrics:       make(map[string]interface{}),
	}

	if err != nil {
		result.ErrorMessage = err.Error()
		result.ExitCode = 1
	} else {
		result.ExitCode = 0
	}

	return result, nil
}

// executeCOBOLJob executes a COBOL job
func (je *JobExecutor) executeCOBOLJob(ctx context.Context, execCtx *ExecutionContext) (*ExecutionResult, error) {
	// COBOL jobs typically require compilation and execution
	return je.executeCompiledJob(ctx, execCtx, "COBOL")
}

// executeDB2Job executes a DB2 job
func (je *JobExecutor) executeDB2Job(ctx context.Context, execCtx *ExecutionContext) (*ExecutionResult, error) {
	startTime := time.Now()
	
	// Prepare DB2 SQL script
	sqlScript, err := je.prepareDB2Script(execCtx)
	if err != nil {
		return nil, fmt.Errorf("failed to prepare DB2 script: %w", err)
	}

	// Execute DB2 commands
	output, err := execCtx.Connection.ExecuteCommand(fmt.Sprintf("DB2 -tf %s", sqlScript))
	
	result := &ExecutionResult{
		Success:       err == nil,
		Output:        output,
		ExecutionTime: time.Since(startTime),
		Metrics:       make(map[string]interface{}),
	}

	if err != nil {
		result.ErrorMessage = err.Error()
		result.ExitCode = 1
	} else {
		result.ExitCode = 0
	}

	return result, nil
}

// executeIMSJob executes an IMS job
func (je *JobExecutor) executeIMSJob(ctx context.Context, execCtx *ExecutionContext) (*ExecutionResult, error) {
	// IMS-specific job execution
	return je.executeMainframeTransaction(ctx, execCtx, "IMS")
}

// executeVSAMJob executes a VSAM job
func (je *JobExecutor) executeVSAMJob(ctx context.Context, execCtx *ExecutionContext) (*ExecutionResult, error) {
	// VSAM-specific job execution
	return je.executeDatasetJob(ctx, execCtx, "VSAM")
}

// executeCICSJob executes a CICS job
func (je *JobExecutor) executeCICSJob(ctx context.Context, execCtx *ExecutionContext) (*ExecutionResult, error) {
	// CICS-specific job execution
	return je.executeMainframeTransaction(ctx, execCtx, "CICS")
}

// executeMQJob executes an MQ job
func (je *JobExecutor) executeMQJob(ctx context.Context, execCtx *ExecutionContext) (*ExecutionResult, error) {
	// MQ-specific job execution
	return je.executeMessageQueueJob(ctx, execCtx)
}

// prepareJCLContent prepares JCL content with parameter substitution
func (je *JobExecutor) prepareJCLContent(execCtx *ExecutionContext) (string, error) {
	jclTemplate := ""
	
	// Get JCL template from job definition or script
	if execCtx.JobDefinition.MainframeConfig.JCLTemplate != nil {
		jclTemplate = *execCtx.JobDefinition.MainframeConfig.JCLTemplate
	} else if execCtx.JobDefinition.Script != nil {
		jclTemplate = *execCtx.JobDefinition.Script
	} else {
		return "", fmt.Errorf("no JCL template or script provided")
	}

	// Substitute parameters
	jclContent := jclTemplate
	for key, value := range execCtx.Parameters {
		placeholder := fmt.Sprintf("${%s}", key)
		jclContent = strings.ReplaceAll(jclContent, placeholder, fmt.Sprintf("%v", value))
	}

	// Substitute environment variables
	for key, value := range execCtx.Environment {
		placeholder := fmt.Sprintf("${ENV.%s}", key)
		jclContent = strings.ReplaceAll(jclContent, placeholder, value)
	}

	return jclContent, nil
}

// prepareREXXScript prepares REXX script content
func (je *JobExecutor) prepareREXXScript(execCtx *ExecutionContext) (string, error) {
	if execCtx.JobDefinition.Script == nil {
		return "", fmt.Errorf("no REXX script provided")
	}

	script := *execCtx.JobDefinition.Script
	
	// Substitute parameters
	for key, value := range execCtx.Parameters {
		placeholder := fmt.Sprintf("${%s}", key)
		script = strings.ReplaceAll(script, placeholder, fmt.Sprintf("%v", value))
	}

	return script, nil
}

// prepareDB2Script prepares DB2 SQL script
func (je *JobExecutor) prepareDB2Script(execCtx *ExecutionContext) (string, error) {
	if execCtx.JobDefinition.Script == nil {
		return "", fmt.Errorf("no DB2 script provided")
	}

	script := *execCtx.JobDefinition.Script
	
	// Substitute parameters
	for key, value := range execCtx.Parameters {
		placeholder := fmt.Sprintf("${%s}", key)
		script = strings.ReplaceAll(script, placeholder, fmt.Sprintf("%v", value))
	}

	return script, nil
}

// monitorJobExecution monitors the execution of a submitted job
func (je *JobExecutor) monitorJobExecution(ctx context.Context, submission *MainframeJobSubmission) (*ExecutionResult, error) {
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		case <-ticker.C:
			// Check job status
			updatedSubmission, err := je.adapter.GetJobStatus(submission.ID)
			if err != nil {
				return nil, fmt.Errorf("failed to get job status: %w", err)
			}

			switch updatedSubmission.Status {
			case "completed":
				return &ExecutionResult{
					Success:            true,
					ExitCode:           *updatedSubmission.ReturnCode,
					Output:             getStringValue(updatedSubmission.JobLog),
					MainframeJobID:     getStringValue(updatedSubmission.MainframeJobID),
					MainframeJobNumber: getStringValue(updatedSubmission.MainframeJobNumber),
					ResourceUsage: map[string]interface{}{
						"cpu_time":     updatedSubmission.CPUTime,
						"elapsed_time": updatedSubmission.ElapsedTime,
						"memory_used":  updatedSubmission.MemoryUsed,
					},
					Metrics: make(map[string]interface{}),
				}, nil
			case "failed":
				return &ExecutionResult{
					Success:            false,
					ExitCode:           getIntValue(updatedSubmission.ReturnCode),
					Output:             getStringValue(updatedSubmission.JobLog),
					ErrorMessage:       getStringValue(updatedSubmission.ErrorMessage),
					MainframeJobID:     getStringValue(updatedSubmission.MainframeJobID),
					MainframeJobNumber: getStringValue(updatedSubmission.MainframeJobNumber),
					Metrics:            make(map[string]interface{}),
				}, nil
			case "cancelled":
				return &ExecutionResult{
					Success:      false,
					ExitCode:     -1,
					ErrorMessage: "job was cancelled",
					Metrics:      make(map[string]interface{}),
				}, nil
			}
		}
	}
}

// performPostExecutionTasks performs tasks after job execution
func (je *JobExecutor) performPostExecutionTasks(execCtx *ExecutionContext, result *ExecutionResult) error {
	// Perform dataset operations if configured
	if execCtx.JobDefinition.MainframeConfig.DatasetOperations != nil {
		for _, dsOp := range execCtx.JobDefinition.MainframeConfig.DatasetOperations {
			if err := je.performDatasetOperation(execCtx, &dsOp); err != nil {
				log.Printf("Dataset operation failed: %v", err)
			}
		}
	}

	// Perform data transfers if configured
	if execCtx.JobDefinition.MainframeConfig.DataTransfers != nil {
		for _, transfer := range execCtx.JobDefinition.MainframeConfig.DataTransfers {
			if err := je.performDataTransfer(execCtx, &transfer); err != nil {
				log.Printf("Data transfer failed: %v", err)
			}
		}
	}

	return nil
}

// Helper functions for other job types (simplified implementations)
func (je *JobExecutor) executeCompiledJob(ctx context.Context, execCtx *ExecutionContext, language string) (*ExecutionResult, error) {
	// Placeholder for compiled job execution (COBOL, etc.)
	return &ExecutionResult{
		Success: true,
		ExitCode: 0,
		Output: fmt.Sprintf("%s job executed successfully", language),
		Metrics: make(map[string]interface{}),
	}, nil
}

func (je *JobExecutor) executeMainframeTransaction(ctx context.Context, execCtx *ExecutionContext, system string) (*ExecutionResult, error) {
	// Placeholder for transaction system execution (IMS, CICS)
	return &ExecutionResult{
		Success: true,
		ExitCode: 0,
		Output: fmt.Sprintf("%s transaction executed successfully", system),
		Metrics: make(map[string]interface{}),
	}, nil
}

func (je *JobExecutor) executeDatasetJob(ctx context.Context, execCtx *ExecutionContext, dsType string) (*ExecutionResult, error) {
	// Placeholder for dataset job execution (VSAM, etc.)
	return &ExecutionResult{
		Success: true,
		ExitCode: 0,
		Output: fmt.Sprintf("%s dataset job executed successfully", dsType),
		Metrics: make(map[string]interface{}),
	}, nil
}

func (je *JobExecutor) executeMessageQueueJob(ctx context.Context, execCtx *ExecutionContext) (*ExecutionResult, error) {
	// Placeholder for MQ job execution
	return &ExecutionResult{
		Success: true,
		ExitCode: 0,
		Output: "MQ job executed successfully",
		Metrics: make(map[string]interface{}),
	}, nil
}

func (je *JobExecutor) performDatasetOperation(execCtx *ExecutionContext, dsOp *models.DatasetOperation) error {
	// Placeholder for dataset operations
	log.Printf("Performing dataset operation: %s on %s", dsOp.Operation, dsOp.DatasetName)
	return nil
}

func (je *JobExecutor) performDataTransfer(execCtx *ExecutionContext, transfer *models.DataTransferConfig) error {
	// Placeholder for data transfers
	log.Printf("Performing data transfer: %s from %s to %s", transfer.TransferType, transfer.SourcePath, transfer.DestinationPath)
	return nil
}

// Helper functions
func getStringValue(ptr *string) string {
	if ptr == nil {
		return ""
	}
	return *ptr
}

func getIntValue(ptr *int) int {
	if ptr == nil {
		return 0
	}
	return *ptr
}
