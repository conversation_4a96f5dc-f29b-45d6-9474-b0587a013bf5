package conversion

import (
	"bufio"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"

	"golang.org/x/text/encoding"
	"golang.org/x/text/encoding/charmap"
	"golang.org/x/text/encoding/unicode"
	"golang.org/x/text/transform"
)

// DataConverter handles data format conversion between mainframe and distributed systems
type DataConverter struct {
	encodings map[string]encoding.Encoding
}

// ConversionOptions holds options for data conversion
type ConversionOptions struct {
	SourceEncoding      string
	TargetEncoding      string
	RecordFormat        string // FB, VB, U
	RecordLength        int
	StripTrailingSpaces bool
	AddLineEndings      bool
	PreserveBinary      bool
}

// RecordProcessor interface for processing different record formats
type RecordProcessor interface {
	ProcessRecord(data []byte) ([]byte, error)
	GetRecordLength() int
	IsVariableLength() bool
}

// FixedBlockProcessor processes fixed-block records
type FixedBlockProcessor struct {
	recordLength int
}

// VariableBlockProcessor processes variable-block records
type VariableBlockProcessor struct {
	maxRecordLength int
}

// UndefinedProcessor processes undefined format records
type UndefinedProcessor struct{}

// NewDataConverter creates a new data converter
func NewDataConverter() *DataConverter {
	converter := &DataConverter{
		encodings: make(map[string]encoding.Encoding),
	}
	
	// Initialize supported encodings
	converter.initializeEncodings()
	
	return converter
}

// initializeEncodings initializes the supported character encodings
func (dc *DataConverter) initializeEncodings() {
	// ASCII/UTF-8 encodings
	dc.encodings["ASCII"] = charmap.Windows1252
	dc.encodings["UTF-8"] = unicode.UTF8
	dc.encodings["UTF-16"] = unicode.UTF16(unicode.BigEndian, unicode.UseBOM)
	dc.encodings["UTF-16LE"] = unicode.UTF16(unicode.LittleEndian, unicode.UseBOM)
	dc.encodings["UTF-16BE"] = unicode.UTF16(unicode.BigEndian, unicode.UseBOM)
	
	// EBCDIC encodings (common mainframe code pages)
	dc.encodings["EBCDIC"] = charmap.CodePage037        // US/Canada EBCDIC
	dc.encodings["EBCDIC-US"] = charmap.CodePage037     // US EBCDIC
	dc.encodings["EBCDIC-INTL"] = charmap.CodePage500   // International EBCDIC
	dc.encodings["EBCDIC-LATIN1"] = charmap.CodePage1047 // Latin-1 EBCDIC
	dc.encodings["CP037"] = charmap.CodePage037         // IBM Code Page 037
	dc.encodings["CP500"] = charmap.CodePage500         // IBM Code Page 500
	dc.encodings["CP1047"] = charmap.CodePage1047       // IBM Code Page 1047
	
	// Other common encodings
	dc.encodings["ISO-8859-1"] = charmap.ISO8859_1
	dc.encodings["WINDOWS-1252"] = charmap.Windows1252
}

// ConvertFileEncoding converts a file from one encoding to another
func (dc *DataConverter) ConvertFileEncoding(filePath, sourceEncoding, targetEncoding string) (string, error) {
	// Get source and target encodings
	srcEnc, err := dc.getEncoding(sourceEncoding)
	if err != nil {
		return "", fmt.Errorf("unsupported source encoding: %s", sourceEncoding)
	}
	
	tgtEnc, err := dc.getEncoding(targetEncoding)
	if err != nil {
		return "", fmt.Errorf("unsupported target encoding: %s", targetEncoding)
	}
	
	// Open source file
	srcFile, err := os.Open(filePath)
	if err != nil {
		return "", fmt.Errorf("failed to open source file: %w", err)
	}
	defer srcFile.Close()
	
	// Create temporary output file
	outputPath := filePath + ".converted"
	outFile, err := os.Create(outputPath)
	if err != nil {
		return "", fmt.Errorf("failed to create output file: %w", err)
	}
	defer outFile.Close()
	
	// Create transformer
	transformer := transform.NewReader(srcFile, srcEnc.NewDecoder())
	writer := transform.NewWriter(outFile, tgtEnc.NewEncoder())
	
	// Copy and convert data
	_, err = io.Copy(writer, transformer)
	if err != nil {
		os.Remove(outputPath)
		return "", fmt.Errorf("encoding conversion failed: %w", err)
	}
	
	return outputPath, nil
}

// ConvertData converts data between encodings
func (dc *DataConverter) ConvertData(data []byte, sourceEncoding, targetEncoding string) ([]byte, error) {
	// Get source and target encodings
	srcEnc, err := dc.getEncoding(sourceEncoding)
	if err != nil {
		return nil, fmt.Errorf("unsupported source encoding: %s", sourceEncoding)
	}
	
	tgtEnc, err := dc.getEncoding(targetEncoding)
	if err != nil {
		return nil, fmt.Errorf("unsupported target encoding: %s", targetEncoding)
	}
	
	// Decode from source encoding
	decoded, err := srcEnc.NewDecoder().Bytes(data)
	if err != nil {
		return nil, fmt.Errorf("failed to decode source data: %w", err)
	}
	
	// Encode to target encoding
	encoded, err := tgtEnc.NewEncoder().Bytes(decoded)
	if err != nil {
		return nil, fmt.Errorf("failed to encode target data: %w", err)
	}
	
	return encoded, nil
}

// ConvertRecordFile converts a file with specific record format
func (dc *DataConverter) ConvertRecordFile(filePath string, options ConversionOptions) (string, error) {
	// Create record processor based on format
	processor, err := dc.createRecordProcessor(options.RecordFormat, options.RecordLength)
	if err != nil {
		return "", fmt.Errorf("failed to create record processor: %w", err)
	}
	
	// Open source file
	srcFile, err := os.Open(filePath)
	if err != nil {
		return "", fmt.Errorf("failed to open source file: %w", err)
	}
	defer srcFile.Close()
	
	// Create output file
	outputPath := filePath + ".converted"
	outFile, err := os.Create(outputPath)
	if err != nil {
		return "", fmt.Errorf("failed to create output file: %w", err)
	}
	defer outFile.Close()
	
	// Process records
	err = dc.processRecords(srcFile, outFile, processor, options)
	if err != nil {
		os.Remove(outputPath)
		return "", fmt.Errorf("record processing failed: %w", err)
	}
	
	return outputPath, nil
}

// processRecords processes records from input to output
func (dc *DataConverter) processRecords(input io.Reader, output io.Writer, processor RecordProcessor, options ConversionOptions) error {
	reader := bufio.NewReader(input)
	writer := bufio.NewWriter(output)
	defer writer.Flush()
	
	if processor.IsVariableLength() {
		return dc.processVariableRecords(reader, writer, processor, options)
	} else {
		return dc.processFixedRecords(reader, writer, processor, options)
	}
}

// processFixedRecords processes fixed-length records
func (dc *DataConverter) processFixedRecords(reader *bufio.Reader, writer *bufio.Writer, processor RecordProcessor, options ConversionOptions) error {
	recordLength := processor.GetRecordLength()
	buffer := make([]byte, recordLength)
	
	for {
		n, err := io.ReadFull(reader, buffer)
		if err == io.EOF {
			break
		}
		if err != nil && err != io.ErrUnexpectedEOF {
			return fmt.Errorf("failed to read record: %w", err)
		}
		
		// Process partial record if at end of file
		if n < recordLength {
			buffer = buffer[:n]
		}
		
		// Process the record
		processedRecord, err := processor.ProcessRecord(buffer)
		if err != nil {
			return fmt.Errorf("failed to process record: %w", err)
		}
		
		// Convert encoding if needed
		if options.SourceEncoding != options.TargetEncoding {
			processedRecord, err = dc.ConvertData(processedRecord, options.SourceEncoding, options.TargetEncoding)
			if err != nil {
				return fmt.Errorf("failed to convert record encoding: %w", err)
			}
		}
		
		// Apply post-processing options
		if options.StripTrailingSpaces {
			processedRecord = []byte(strings.TrimRight(string(processedRecord), " "))
		}
		
		// Write processed record
		_, err = writer.Write(processedRecord)
		if err != nil {
			return fmt.Errorf("failed to write record: %w", err)
		}
		
		// Add line ending if requested
		if options.AddLineEndings {
			_, err = writer.WriteString("\n")
			if err != nil {
				return fmt.Errorf("failed to write line ending: %w", err)
			}
		}
	}
	
	return nil
}

// processVariableRecords processes variable-length records
func (dc *DataConverter) processVariableRecords(reader *bufio.Reader, writer *bufio.Writer, processor RecordProcessor, options ConversionOptions) error {
	for {
		// Read record length (typically first 4 bytes for VB format)
		lengthBytes := make([]byte, 4)
		_, err := io.ReadFull(reader, lengthBytes)
		if err == io.EOF {
			break
		}
		if err != nil {
			return fmt.Errorf("failed to read record length: %w", err)
		}
		
		// Extract record length (big-endian format)
		recordLength := int(lengthBytes[0])<<24 | int(lengthBytes[1])<<16 | int(lengthBytes[2])<<8 | int(lengthBytes[3])
		
		// Validate record length
		if recordLength < 4 || recordLength > 32760 { // Typical VB limits
			return fmt.Errorf("invalid record length: %d", recordLength)
		}
		
		// Read the actual record data (excluding the length prefix)
		recordData := make([]byte, recordLength-4)
		_, err = io.ReadFull(reader, recordData)
		if err != nil {
			return fmt.Errorf("failed to read record data: %w", err)
		}
		
		// Process the record
		processedRecord, err := processor.ProcessRecord(recordData)
		if err != nil {
			return fmt.Errorf("failed to process record: %w", err)
		}
		
		// Convert encoding if needed
		if options.SourceEncoding != options.TargetEncoding {
			processedRecord, err = dc.ConvertData(processedRecord, options.SourceEncoding, options.TargetEncoding)
			if err != nil {
				return fmt.Errorf("failed to convert record encoding: %w", err)
			}
		}
		
		// Apply post-processing options
		if options.StripTrailingSpaces {
			processedRecord = []byte(strings.TrimRight(string(processedRecord), " "))
		}
		
		// Write processed record
		_, err = writer.Write(processedRecord)
		if err != nil {
			return fmt.Errorf("failed to write record: %w", err)
		}
		
		// Add line ending if requested
		if options.AddLineEndings {
			_, err = writer.WriteString("\n")
			if err != nil {
				return fmt.Errorf("failed to write line ending: %w", err)
			}
		}
	}
	
	return nil
}

// createRecordProcessor creates a record processor based on format
func (dc *DataConverter) createRecordProcessor(format string, recordLength int) (RecordProcessor, error) {
	switch strings.ToUpper(format) {
	case "FB": // Fixed Block
		return &FixedBlockProcessor{recordLength: recordLength}, nil
	case "VB": // Variable Block
		return &VariableBlockProcessor{maxRecordLength: recordLength}, nil
	case "U": // Undefined
		return &UndefinedProcessor{}, nil
	default:
		return nil, fmt.Errorf("unsupported record format: %s", format)
	}
}

// getEncoding returns the encoding for a given name
func (dc *DataConverter) getEncoding(name string) (encoding.Encoding, error) {
	enc, exists := dc.encodings[strings.ToUpper(name)]
	if !exists {
		return nil, fmt.Errorf("encoding not found: %s", name)
	}
	return enc, nil
}

// GetSupportedEncodings returns a list of supported encodings
func (dc *DataConverter) GetSupportedEncodings() []string {
	encodings := make([]string, 0, len(dc.encodings))
	for name := range dc.encodings {
		encodings = append(encodings, name)
	}
	return encodings
}

// FixedBlockProcessor implementation
func (fbp *FixedBlockProcessor) ProcessRecord(data []byte) ([]byte, error) {
	// For fixed block, just return the data as-is
	return data, nil
}

func (fbp *FixedBlockProcessor) GetRecordLength() int {
	return fbp.recordLength
}

func (fbp *FixedBlockProcessor) IsVariableLength() bool {
	return false
}

// VariableBlockProcessor implementation
func (vbp *VariableBlockProcessor) ProcessRecord(data []byte) ([]byte, error) {
	// For variable block, return the data without the length prefix
	return data, nil
}

func (vbp *VariableBlockProcessor) GetRecordLength() int {
	return vbp.maxRecordLength
}

func (vbp *VariableBlockProcessor) IsVariableLength() bool {
	return true
}

// UndefinedProcessor implementation
func (up *UndefinedProcessor) ProcessRecord(data []byte) ([]byte, error) {
	// For undefined format, return data as-is
	return data, nil
}

func (up *UndefinedProcessor) GetRecordLength() int {
	return 0 // Variable length
}

func (up *UndefinedProcessor) IsVariableLength() bool {
	return true
}
