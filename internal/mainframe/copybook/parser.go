package copybook

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"
)

// <PERSON><PERSON><PERSON> handles COBOL copybook parsing
type Parser struct {
	// Configuration options
	options ParseOptions
}

// ParseOptions holds options for copybook parsing
type ParseOptions struct {
	IgnoreComments     bool
	TrimWhitespace     bool
	CaseSensitive      bool
	DefaultPicture     string
	DefaultUsage       string
}

// Field represents a field in a COBOL copybook
type Field struct {
	Level       int                    `json:"level"`
	Name        string                 `json:"name"`
	Picture     string                 `json:"picture,omitempty"`
	Usage       string                 `json:"usage,omitempty"`
	Value       string                 `json:"value,omitempty"`
	Occurs      int                    `json:"occurs,omitempty"`
	Redefines   string                 `json:"redefines,omitempty"`
	Offset      int                    `json:"offset"`
	Length      int                    `json:"length"`
	Type        string                 `json:"type"`
	Children    []Field                `json:"children,omitempty"`
	Attributes  map[string]interface{} `json:"attributes,omitempty"`
}

// CopybookStructure represents the parsed structure of a copybook
type CopybookStructure struct {
	Name        string                 `json:"name"`
	Fields      []Field                `json:"fields"`
	TotalLength int                    `json:"total_length"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// DataType represents different COBOL data types
type DataType struct {
	Name        string
	Pattern     *regexp.Regexp
	Length      func(picture string) int
	Alignment   int
	IsNumeric   bool
	IsSigned    bool
	HasDecimals bool
}

// NewParser creates a new copybook parser
func NewParser() *Parser {
	return &Parser{
		options: ParseOptions{
			IgnoreComments:  true,
			TrimWhitespace:  true,
			CaseSensitive:   false,
			DefaultPicture:  "X",
			DefaultUsage:    "DISPLAY",
		},
	}
}

// SetOptions sets parsing options
func (p *Parser) SetOptions(options ParseOptions) {
	p.options = options
}

// ParseCopybook parses a COBOL copybook and returns the structure
func (p *Parser) ParseCopybook(copybookContent string) (*CopybookStructure, error) {
	// Clean and prepare the copybook content
	lines := p.preprocessCopybook(copybookContent)
	
	// Parse the fields
	fields, err := p.parseFields(lines)
	if err != nil {
		return nil, fmt.Errorf("failed to parse fields: %w", err)
	}
	
	// Calculate offsets and lengths
	totalLength, err := p.calculateLayout(fields)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate layout: %w", err)
	}
	
	structure := &CopybookStructure{
		Fields:      fields,
		TotalLength: totalLength,
		Metadata: map[string]interface{}{
			"parsed_at": "now",
			"parser":    "workflowmaster-copybook-parser",
		},
	}
	
	return structure, nil
}

// preprocessCopybook cleans and prepares copybook content for parsing
func (p *Parser) preprocessCopybook(content string) []string {
	lines := strings.Split(content, "\n")
	var cleanLines []string
	
	for _, line := range lines {
		// Remove sequence numbers (columns 1-6) and identification area (columns 73-80)
		if len(line) > 72 {
			line = line[6:72]
		} else if len(line) > 6 {
			line = line[6:]
		}
		
		// Skip comment lines (asterisk in column 7)
		if p.options.IgnoreComments && len(line) > 0 && line[0] == '*' {
			continue
		}
		
		// Skip empty lines
		if p.options.TrimWhitespace {
			line = strings.TrimSpace(line)
			if line == "" {
				continue
			}
		}
		
		// Handle continuation lines (hyphen in column 7)
		if len(line) > 0 && line[0] == '-' {
			if len(cleanLines) > 0 {
				cleanLines[len(cleanLines)-1] += " " + strings.TrimSpace(line[1:])
			}
			continue
		}
		
		cleanLines = append(cleanLines, line)
	}
	
	return cleanLines
}

// parseFields parses individual fields from copybook lines
func (p *Parser) parseFields(lines []string) ([]Field, error) {
	var fields []Field
	var stack []int // Stack to track parent levels
	
	for lineNum, line := range lines {
		field, err := p.parseLine(line, lineNum+1)
		if err != nil {
			return nil, fmt.Errorf("error parsing line %d: %w", lineNum+1, err)
		}
		
		if field == nil {
			continue // Skip non-field lines
		}
		
		// Handle field hierarchy
		fields = p.addFieldToHierarchy(fields, *field, &stack)
	}
	
	return fields, nil
}

// parseLine parses a single line of copybook
func (p *Parser) parseLine(line string, lineNum int) (*Field, error) {
	// Skip non-field lines
	if !p.isFieldLine(line) {
		return nil, nil
	}
	
	// Regular expression to parse COBOL field definition
	// Pattern: level-number field-name [REDEFINES field] [PIC picture] [USAGE usage] [VALUE value] [OCCURS times]
	fieldRegex := regexp.MustCompile(`^\s*(\d{1,2})\s+([A-Za-z0-9\-_]+)(?:\s+REDEFINES\s+([A-Za-z0-9\-_]+))?(?:\s+(?:PIC|PICTURE)\s+([^\s]+))?(?:\s+USAGE\s+([^\s]+))?(?:\s+VALUE\s+([^\s.]+))?(?:\s+OCCURS\s+(\d+))?`)
	
	matches := fieldRegex.FindStringSubmatch(line)
	if len(matches) < 3 {
		return nil, fmt.Errorf("invalid field definition: %s", line)
	}
	
	// Parse level number
	level, err := strconv.Atoi(matches[1])
	if err != nil {
		return nil, fmt.Errorf("invalid level number: %s", matches[1])
	}
	
	field := Field{
		Level:      level,
		Name:       matches[2],
		Attributes: make(map[string]interface{}),
	}
	
	// Parse optional clauses
	if len(matches) > 3 && matches[3] != "" {
		field.Redefines = matches[3]
	}
	
	if len(matches) > 4 && matches[4] != "" {
		field.Picture = matches[4]
	}
	
	if len(matches) > 5 && matches[5] != "" {
		field.Usage = matches[5]
	} else {
		field.Usage = p.options.DefaultUsage
	}
	
	if len(matches) > 6 && matches[6] != "" {
		field.Value = matches[6]
	}
	
	if len(matches) > 7 && matches[7] != "" {
		occurs, err := strconv.Atoi(matches[7])
		if err == nil {
			field.Occurs = occurs
		}
	}
	
	// Determine field type and calculate length
	field.Type, field.Length = p.determineFieldType(field.Picture, field.Usage)
	
	return &field, nil
}

// isFieldLine determines if a line contains a field definition
func (p *Parser) isFieldLine(line string) bool {
	// Check if line starts with a level number (01-49, 66, 77, 88)
	levelRegex := regexp.MustCompile(`^\s*(?:0[1-9]|[1-4][0-9]|66|77|88)\s+`)
	return levelRegex.MatchString(line)
}

// addFieldToHierarchy adds a field to the appropriate position in the hierarchy
func (p *Parser) addFieldToHierarchy(fields []Field, field Field, stack *[]int) []Field {
	// Adjust stack based on current field level
	for len(*stack) > 0 && (*stack)[len(*stack)-1] >= field.Level {
		*stack = (*stack)[:len(*stack)-1]
	}
	
	if len(*stack) == 0 {
		// Top-level field
		fields = append(fields, field)
		*stack = append(*stack, field.Level)
	} else {
		// Child field - find the parent
		parent := p.findParentField(&fields, *stack)
		if parent != nil {
			parent.Children = append(parent.Children, field)
			*stack = append(*stack, field.Level)
		}
	}
	
	return fields
}

// findParentField finds the parent field for the current field
func (p *Parser) findParentField(fields *[]Field, stack []int) *Field {
	if len(stack) == 0 {
		return nil
	}
	
	// Navigate to the parent field using the stack
	current := fields
	for i := 0; i < len(stack)-1; i++ {
		if len(*current) == 0 {
			return nil
		}
		parent := &(*current)[len(*current)-1]
		current = &parent.Children
	}
	
	if len(*current) > 0 {
		return &(*current)[len(*current)-1]
	}
	
	return nil
}

// determineFieldType determines the field type and length based on picture and usage
func (p *Parser) determineFieldType(picture, usage string) (string, int) {
	if picture == "" {
		picture = p.options.DefaultPicture
	}
	
	// Define data type patterns
	dataTypes := []DataType{
		{
			Name:        "NUMERIC_DISPLAY",
			Pattern:     regexp.MustCompile(`^9+(\((\d+)\))?$`),
			Length:      func(pic string) int { return p.calculateNumericLength(pic) },
			IsNumeric:   true,
		},
		{
			Name:        "NUMERIC_SIGNED",
			Pattern:     regexp.MustCompile(`^S9+(\((\d+)\))?$`),
			Length:      func(pic string) int { return p.calculateNumericLength(pic) },
			IsNumeric:   true,
			IsSigned:    true,
		},
		{
			Name:        "NUMERIC_DECIMAL",
			Pattern:     regexp.MustCompile(`^9+V9+(\((\d+)\))?$`),
			Length:      func(pic string) int { return p.calculateDecimalLength(pic) },
			IsNumeric:   true,
			HasDecimals: true,
		},
		{
			Name:        "ALPHANUMERIC",
			Pattern:     regexp.MustCompile(`^X+(\((\d+)\))?$`),
			Length:      func(pic string) int { return p.calculateAlphanumericLength(pic) },
		},
		{
			Name:        "ALPHABETIC",
			Pattern:     regexp.MustCompile(`^A+(\((\d+)\))?$`),
			Length:      func(pic string) int { return p.calculateAlphabeticLength(pic) },
		},
	}
	
	// Match picture against data types
	for _, dataType := range dataTypes {
		if dataType.Pattern.MatchString(picture) {
			length := dataType.Length(picture)
			
			// Adjust length based on usage
			switch strings.ToUpper(usage) {
			case "COMP", "COMPUTATIONAL":
				if dataType.IsNumeric {
					return "BINARY", (length + 1) / 2
				}
			case "COMP-3", "PACKED-DECIMAL":
				if dataType.IsNumeric {
					return "PACKED", (length + 1) / 2
				}
			case "COMP-1":
				return "FLOAT", 4
			case "COMP-2":
				return "DOUBLE", 8
			}
			
			return dataType.Name, length
		}
	}
	
	// Default to alphanumeric
	return "ALPHANUMERIC", 1
}

// calculateNumericLength calculates length for numeric fields
func (p *Parser) calculateNumericLength(picture string) int {
	// Handle patterns like 9(5) or 999
	if strings.Contains(picture, "(") {
		re := regexp.MustCompile(`\((\d+)\)`)
		matches := re.FindStringSubmatch(picture)
		if len(matches) > 1 {
			if length, err := strconv.Atoi(matches[1]); err == nil {
				return length
			}
		}
	}
	
	// Count the number of 9s
	return strings.Count(picture, "9")
}

// calculateDecimalLength calculates length for decimal fields
func (p *Parser) calculateDecimalLength(picture string) int {
	// Handle patterns like 9(3)V9(2)
	totalLength := 0
	
	// Count digits before decimal point
	beforeV := strings.Split(picture, "V")[0]
	totalLength += p.calculateNumericLength(beforeV)
	
	// Count digits after decimal point
	if strings.Contains(picture, "V") {
		afterV := strings.Split(picture, "V")[1]
		totalLength += p.calculateNumericLength(afterV)
	}
	
	return totalLength
}

// calculateAlphanumericLength calculates length for alphanumeric fields
func (p *Parser) calculateAlphanumericLength(picture string) int {
	// Handle patterns like X(10) or XXX
	if strings.Contains(picture, "(") {
		re := regexp.MustCompile(`\((\d+)\)`)
		matches := re.FindStringSubmatch(picture)
		if len(matches) > 1 {
			if length, err := strconv.Atoi(matches[1]); err == nil {
				return length
			}
		}
	}
	
	// Count the number of Xs
	return strings.Count(picture, "X")
}

// calculateAlphabeticLength calculates length for alphabetic fields
func (p *Parser) calculateAlphabeticLength(picture string) int {
	// Handle patterns like A(10) or AAA
	if strings.Contains(picture, "(") {
		re := regexp.MustCompile(`\((\d+)\)`)
		matches := re.FindStringSubmatch(picture)
		if len(matches) > 1 {
			if length, err := strconv.Atoi(matches[1]); err == nil {
				return length
			}
		}
	}
	
	// Count the number of As
	return strings.Count(picture, "A")
}

// calculateLayout calculates field offsets and total record length
func (p *Parser) calculateLayout(fields []Field) (int, error) {
	offset := 0
	
	for i := range fields {
		offset = p.calculateFieldLayout(&fields[i], offset)
	}
	
	return offset, nil
}

// calculateFieldLayout calculates layout for a single field and its children
func (p *Parser) calculateFieldLayout(field *Field, startOffset int) int {
	field.Offset = startOffset
	currentOffset := startOffset
	
	if len(field.Children) > 0 {
		// Group field - calculate children layout
		for i := range field.Children {
			currentOffset = p.calculateFieldLayout(&field.Children[i], currentOffset)
		}
		field.Length = currentOffset - startOffset
	} else {
		// Elementary field
		fieldLength := field.Length
		if field.Occurs > 0 {
			fieldLength *= field.Occurs
		}
		currentOffset = startOffset + fieldLength
		field.Length = fieldLength
	}
	
	return currentOffset
}

// ExtractData extracts data from a record using the copybook structure
func (p *Parser) ExtractData(record []byte, structure *CopybookStructure) (map[string]interface{}, error) {
	data := make(map[string]interface{})
	
	for _, field := range structure.Fields {
		value, err := p.extractFieldData(record, &field)
		if err != nil {
			return nil, fmt.Errorf("failed to extract field %s: %w", field.Name, err)
		}
		data[field.Name] = value
	}
	
	return data, nil
}

// extractFieldData extracts data for a single field
func (p *Parser) extractFieldData(record []byte, field *Field) (interface{}, error) {
	if field.Offset+field.Length > len(record) {
		return nil, fmt.Errorf("field %s extends beyond record length", field.Name)
	}
	
	fieldData := record[field.Offset : field.Offset+field.Length]
	
	if len(field.Children) > 0 {
		// Group field - extract children
		groupData := make(map[string]interface{})
		for _, child := range field.Children {
			value, err := p.extractFieldData(record, &child)
			if err != nil {
				return nil, err
			}
			groupData[child.Name] = value
		}
		return groupData, nil
	}
	
	// Elementary field - convert based on type
	return p.convertFieldData(fieldData, field)
}

// convertFieldData converts raw field data to appropriate Go type
func (p *Parser) convertFieldData(data []byte, field *Field) (interface{}, error) {
	switch field.Type {
	case "NUMERIC_DISPLAY", "NUMERIC_SIGNED":
		return p.convertNumericDisplay(data, field.IsSigned)
	case "NUMERIC_DECIMAL":
		return p.convertNumericDecimal(data)
	case "BINARY":
		return p.convertBinary(data)
	case "PACKED":
		return p.convertPacked(data)
	case "FLOAT":
		return p.convertFloat(data)
	case "DOUBLE":
		return p.convertDouble(data)
	default:
		// Alphanumeric or alphabetic
		return strings.TrimRight(string(data), " "), nil
	}
}

// convertNumericDisplay converts display numeric data
func (p *Parser) convertNumericDisplay(data []byte, signed bool) (interface{}, error) {
	str := strings.TrimSpace(string(data))
	if str == "" {
		return 0, nil
	}
	
	if signed && len(str) > 0 {
		// Handle signed numeric (last character might be sign)
		lastChar := str[len(str)-1]
		if lastChar >= 'A' && lastChar <= 'R' {
			// Positive sign
			str = str[:len(str)-1] + string(rune(lastChar-'A'+'0'))
		} else if lastChar >= 'J' && lastChar <= 'R' {
			// Negative sign
			str = "-" + str[:len(str)-1] + string(rune(lastChar-'J'+'0'))
		}
	}
	
	return strconv.ParseInt(str, 10, 64)
}

// convertNumericDecimal converts decimal numeric data
func (p *Parser) convertNumericDecimal(data []byte) (interface{}, error) {
	str := strings.TrimSpace(string(data))
	if str == "" {
		return 0.0, nil
	}
	
	return strconv.ParseFloat(str, 64)
}

// convertBinary converts binary data
func (p *Parser) convertBinary(data []byte) (interface{}, error) {
	// Convert binary data to integer
	var result int64
	for _, b := range data {
		result = result<<8 + int64(b)
	}
	return result, nil
}

// convertPacked converts packed decimal data
func (p *Parser) convertPacked(data []byte) (interface{}, error) {
	// Simplified packed decimal conversion
	// In practice, this would need more sophisticated handling
	var result int64
	for i, b := range data {
		if i == len(data)-1 {
			// Last byte contains sign in lower nibble
			result = result*10 + int64((b&0xF0)>>4)
			if (b & 0x0F) == 0x0D {
				result = -result // Negative
			}
		} else {
			// Each byte contains two digits
			result = result*100 + int64((b&0xF0)>>4)*10 + int64(b&0x0F)
		}
	}
	return result, nil
}

// convertFloat converts single-precision float
func (p *Parser) convertFloat(data []byte) (interface{}, error) {
	if len(data) != 4 {
		return nil, fmt.Errorf("invalid float data length: %d", len(data))
	}
	// Implementation would depend on the specific float format used
	return 0.0, nil
}

// convertDouble converts double-precision float
func (p *Parser) convertDouble(data []byte) (interface{}, error) {
	if len(data) != 8 {
		return nil, fmt.Errorf("invalid double data length: %d", len(data))
	}
	// Implementation would depend on the specific double format used
	return 0.0, nil
}
