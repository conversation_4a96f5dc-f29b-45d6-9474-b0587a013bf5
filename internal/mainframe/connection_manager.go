package mainframe

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/workflowmaster/workflowmaster/internal/database"
)

// ConnectionManager manages mainframe connections and connection pooling
type ConnectionManager struct {
	db              *database.Database
	config          *AdapterConfig
	
	// Connection pools by connection ID
	connectionPools map[uuid.UUID]*ConnectionPool
	poolMutex       sync.RWMutex
	
	// Active connections
	activeConnections map[uuid.UUID]Connection
	connectionMutex   sync.RWMutex
	
	// Monitoring
	ctx             context.Context
	running         bool
}

// ConnectionPool manages a pool of connections to a specific mainframe system
type ConnectionPool struct {
	connectionConfig *MainframeConnection
	connections      []Connection
	available        []Connection
	inUse           []Connection
	mutex           sync.Mutex
	
	// Pool configuration
	maxConnections  int
	minConnections  int
	idleTimeout     time.Duration
	maxLifetime     time.Duration
	
	// Statistics
	totalCreated    int64
	totalDestroyed  int64
	totalBorrowed   int64
	totalReturned   int64
}

// NewConnectionManager creates a new connection manager
func NewConnectionManager(db *database.Database, config *AdapterConfig) *ConnectionManager {
	return &ConnectionManager{
		db:                db,
		config:            config,
		connectionPools:   make(map[uuid.UUID]*ConnectionPool),
		activeConnections: make(map[uuid.UUID]Connection),
		running:           false,
	}
}

// Start starts the connection manager
func (cm *ConnectionManager) Start(ctx context.Context) error {
	cm.ctx = ctx
	cm.running = true
	
	// Start connection monitoring goroutine
	go cm.monitorConnections()
	
	// Load existing connections and create pools
	if err := cm.loadConnectionPools(); err != nil {
		return fmt.Errorf("failed to load connection pools: %w", err)
	}
	
	log.Println("Connection manager started")
	return nil
}

// Stop stops the connection manager
func (cm *ConnectionManager) Stop() {
	cm.running = false
	
	// Close all connection pools
	cm.poolMutex.Lock()
	for _, pool := range cm.connectionPools {
		cm.closeConnectionPool(pool)
	}
	cm.connectionPools = make(map[uuid.UUID]*ConnectionPool)
	cm.poolMutex.Unlock()
	
	// Close all active connections
	cm.connectionMutex.Lock()
	for _, conn := range cm.activeConnections {
		conn.Close()
	}
	cm.activeConnections = make(map[uuid.UUID]Connection)
	cm.connectionMutex.Unlock()
	
	log.Println("Connection manager stopped")
}

// GetConnection gets a connection from the pool or creates a new one
func (cm *ConnectionManager) GetConnection(connectionID uuid.UUID) (Connection, error) {
	// Get or create connection pool
	pool, err := cm.getOrCreateConnectionPool(connectionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get connection pool: %w", err)
	}
	
	// Borrow connection from pool
	conn, err := cm.borrowConnection(pool)
	if err != nil {
		return nil, fmt.Errorf("failed to borrow connection: %w", err)
	}
	
	// Track active connection
	cm.connectionMutex.Lock()
	cm.activeConnections[conn.GetConnectionID()] = conn
	cm.connectionMutex.Unlock()
	
	return conn, nil
}

// ReturnConnection returns a connection to the pool
func (cm *ConnectionManager) ReturnConnection(conn Connection) error {
	connectionID := conn.GetConnectionID()
	
	// Remove from active connections
	cm.connectionMutex.Lock()
	delete(cm.activeConnections, connectionID)
	cm.connectionMutex.Unlock()
	
	// Find the appropriate pool and return connection
	cm.poolMutex.RLock()
	defer cm.poolMutex.RUnlock()
	
	for _, pool := range cm.connectionPools {
		if cm.returnConnectionToPool(pool, conn) {
			return nil
		}
	}
	
	// If no pool found, close the connection
	return conn.Close()
}

// TestConnection tests connectivity to a mainframe system
func (cm *ConnectionManager) TestConnection(connection *MainframeConnection) error {
	// Create a temporary connection for testing
	conn, err := cm.createConnection(connection)
	if err != nil {
		return fmt.Errorf("failed to create test connection: %w", err)
	}
	defer conn.Close()
	
	// Test basic connectivity
	if !conn.IsConnected() {
		return fmt.Errorf("connection test failed: not connected")
	}
	
	// Try to execute a simple command
	_, err = conn.ExecuteCommand("TIME") // Simple command that should work on most mainframes
	if err != nil {
		return fmt.Errorf("connection test failed: %w", err)
	}
	
	return nil
}

// loadConnectionPools loads existing connections and creates pools
func (cm *ConnectionManager) loadConnectionPools() error {
	var connections []MainframeConnection
	if err := cm.db.DB.Where("status = ?", "active").Find(&connections).Error; err != nil {
		return err
	}
	
	for _, connection := range connections {
		pool := cm.createConnectionPool(&connection)
		cm.poolMutex.Lock()
		cm.connectionPools[connection.ID] = pool
		cm.poolMutex.Unlock()
		
		log.Printf("Created connection pool for: %s", connection.Name)
	}
	
	return nil
}

// getOrCreateConnectionPool gets an existing pool or creates a new one
func (cm *ConnectionManager) getOrCreateConnectionPool(connectionID uuid.UUID) (*ConnectionPool, error) {
	cm.poolMutex.RLock()
	pool, exists := cm.connectionPools[connectionID]
	cm.poolMutex.RUnlock()
	
	if exists {
		return pool, nil
	}
	
	// Load connection configuration
	var connection MainframeConnection
	if err := cm.db.DB.First(&connection, connectionID).Error; err != nil {
		return nil, fmt.Errorf("connection not found: %w", err)
	}
	
	// Create new pool
	pool = cm.createConnectionPool(&connection)
	
	cm.poolMutex.Lock()
	cm.connectionPools[connectionID] = pool
	cm.poolMutex.Unlock()
	
	return pool, nil
}

// createConnectionPool creates a new connection pool
func (cm *ConnectionManager) createConnectionPool(connection *MainframeConnection) *ConnectionPool {
	return &ConnectionPool{
		connectionConfig: connection,
		connections:      make([]Connection, 0),
		available:        make([]Connection, 0),
		inUse:           make([]Connection, 0),
		maxConnections:  connection.MaxConnections,
		minConnections:  1,
		idleTimeout:     time.Duration(connection.Timeout) * time.Second,
		maxLifetime:     1 * time.Hour,
	}
}

// borrowConnection borrows a connection from the pool
func (cm *ConnectionManager) borrowConnection(pool *ConnectionPool) (Connection, error) {
	pool.mutex.Lock()
	defer pool.mutex.Unlock()
	
	// Try to get an available connection
	if len(pool.available) > 0 {
		conn := pool.available[0]
		pool.available = pool.available[1:]
		pool.inUse = append(pool.inUse, conn)
		pool.totalBorrowed++
		
		// Verify connection is still valid
		if conn.IsConnected() {
			return conn, nil
		}
		
		// Connection is stale, close it and create a new one
		conn.Close()
		pool.totalDestroyed++
	}
	
	// Create new connection if under limit
	if len(pool.connections) < pool.maxConnections {
		conn, err := cm.createConnection(pool.connectionConfig)
		if err != nil {
			return nil, err
		}
		
		pool.connections = append(pool.connections, conn)
		pool.inUse = append(pool.inUse, conn)
		pool.totalCreated++
		pool.totalBorrowed++
		
		return conn, nil
	}
	
	return nil, fmt.Errorf("connection pool exhausted")
}

// returnConnectionToPool returns a connection to the appropriate pool
func (cm *ConnectionManager) returnConnectionToPool(pool *ConnectionPool, conn Connection) bool {
	pool.mutex.Lock()
	defer pool.mutex.Unlock()
	
	// Find and remove from in-use list
	for i, inUseConn := range pool.inUse {
		if inUseConn.GetConnectionID() == conn.GetConnectionID() {
			pool.inUse = append(pool.inUse[:i], pool.inUse[i+1:]...)
			
			// Add to available list if connection is still valid
			if conn.IsConnected() {
				pool.available = append(pool.available, conn)
			} else {
				conn.Close()
				pool.totalDestroyed++
			}
			
			pool.totalReturned++
			return true
		}
	}
	
	return false
}

// createConnection creates a new connection to a mainframe system
func (cm *ConnectionManager) createConnection(connection *MainframeConnection) (Connection, error) {
	// Determine protocol based on connection settings
	protocol := "telnet" // Default protocol
	if connection.ProtocolSettings != nil {
		if p, ok := connection.ProtocolSettings["protocol"].(string); ok {
			protocol = p
		}
	}
	
	// Get protocol handler
	var handler ProtocolHandler
	switch protocol {
	case "ftp":
		handler = NewFTPHandler(cm.config.FTPConfig)
	case "sftp":
		handler = NewSFTPHandler(cm.config.SFTPConfig)
	case "telnet":
		handler = NewTelnetHandler(cm.config.TelnetConfig)
	case "tcp":
		handler = NewTCPHandler()
	default:
		return nil, fmt.Errorf("unsupported protocol: %s", protocol)
	}
	
	// Create connection
	conn, err := handler.Connect(connection)
	if err != nil {
		return nil, fmt.Errorf("failed to connect: %w", err)
	}
	
	return conn, nil
}

// closeConnectionPool closes all connections in a pool
func (cm *ConnectionManager) closeConnectionPool(pool *ConnectionPool) {
	pool.mutex.Lock()
	defer pool.mutex.Unlock()
	
	// Close all connections
	for _, conn := range pool.connections {
		conn.Close()
		pool.totalDestroyed++
	}
	
	pool.connections = nil
	pool.available = nil
	pool.inUse = nil
}

// monitorConnections monitors connection health and performs cleanup
func (cm *ConnectionManager) monitorConnections() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()
	
	for {
		select {
		case <-cm.ctx.Done():
			return
		case <-ticker.C:
			if cm.running {
				cm.cleanupStaleConnections()
				cm.updateConnectionStatus()
			}
		}
	}
}

// cleanupStaleConnections removes stale connections from pools
func (cm *ConnectionManager) cleanupStaleConnections() {
	cm.poolMutex.RLock()
	defer cm.poolMutex.RUnlock()
	
	for _, pool := range cm.connectionPools {
		pool.mutex.Lock()
		
		// Check available connections for staleness
		var validConnections []Connection
		for _, conn := range pool.available {
			if conn.IsConnected() && time.Since(conn.GetLastActivity()) < pool.idleTimeout {
				validConnections = append(validConnections, conn)
			} else {
				conn.Close()
				pool.totalDestroyed++
			}
		}
		pool.available = validConnections
		
		pool.mutex.Unlock()
	}
}

// updateConnectionStatus updates connection status in database
func (cm *ConnectionManager) updateConnectionStatus() {
	cm.poolMutex.RLock()
	defer cm.poolMutex.RUnlock()
	
	for connectionID, pool := range cm.connectionPools {
		pool.mutex.Lock()
		activeCount := len(pool.inUse)
		availableCount := len(pool.available)
		pool.mutex.Unlock()
		
		// Update database with current status
		cm.db.DB.Model(&MainframeConnection{}).
			Where("id = ?", connectionID).
			Updates(map[string]interface{}{
				"last_connected": time.Now(),
				"status":         "active",
			})
		
		log.Printf("Connection pool %s: %d active, %d available", 
			connectionID, activeCount, availableCount)
	}
}

// GetActiveConnectionCount returns the number of active connections
func (cm *ConnectionManager) GetActiveConnectionCount() int {
	cm.connectionMutex.RLock()
	defer cm.connectionMutex.RUnlock()
	return len(cm.activeConnections)
}

// GetPoolStats returns statistics for all connection pools
func (cm *ConnectionManager) GetPoolStats() map[uuid.UUID]map[string]interface{} {
	cm.poolMutex.RLock()
	defer cm.poolMutex.RUnlock()
	
	stats := make(map[uuid.UUID]map[string]interface{})
	
	for connectionID, pool := range cm.connectionPools {
		pool.mutex.Lock()
		stats[connectionID] = map[string]interface{}{
			"total_connections": len(pool.connections),
			"available":         len(pool.available),
			"in_use":           len(pool.inUse),
			"total_created":    pool.totalCreated,
			"total_destroyed":  pool.totalDestroyed,
			"total_borrowed":   pool.totalBorrowed,
			"total_returned":   pool.totalReturned,
		}
		pool.mutex.Unlock()
	}
	
	return stats
}
