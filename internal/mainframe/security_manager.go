package mainframe

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"io"
	"log"
	"strings"
	"time"

	"golang.org/x/crypto/pbkdf2"
)

// SecurityManager handles mainframe security integration
type SecurityManager struct {
	config        *AdapterConfig
	encryptionKey []byte
	gcm           cipher.AEAD
}

// SecuritySystem represents different mainframe security systems
type SecuritySystem interface {
	Authenticate(username, password string) (*SecurityContext, error)
	Authorize(context *SecurityContext, resource string, action string) (bool, error)
	GetUserProfile(username string) (*UserProfile, error)
	ValidatePassword(username, password string) error
	ChangePassword(username, oldPassword, newPassword string) error
	GetSecurityGroups(username string) ([]string, error)
}

// SecurityContext holds security information for a user session
type SecurityContext struct {
	UserID          string                 `json:"user_id"`
	Username        string                 `json:"username"`
	SecuritySystem  string                 `json:"security_system"`
	Groups          []string               `json:"groups"`
	Permissions     []string               `json:"permissions"`
	SessionID       string                 `json:"session_id"`
	ExpiresAt       time.Time              `json:"expires_at"`
	Attributes      map[string]interface{} `json:"attributes"`
}

// UserProfile represents a mainframe user profile
type UserProfile struct {
	UserID          string                 `json:"user_id"`
	Username        string                 `json:"username"`
	FullName        string                 `json:"full_name"`
	Email           string                 `json:"email"`
	Department      string                 `json:"department"`
	Groups          []string               `json:"groups"`
	Permissions     []string               `json:"permissions"`
	LastLogin       *time.Time             `json:"last_login"`
	PasswordExpiry  *time.Time             `json:"password_expiry"`
	AccountStatus   string                 `json:"account_status"`
	Attributes      map[string]interface{} `json:"attributes"`
}

// RACFSecuritySystem implements RACF security system integration
type RACFSecuritySystem struct {
	connection Connection
	config     *RACFConfig
}

// ACF2SecuritySystem implements ACF2 security system integration
type ACF2SecuritySystem struct {
	connection Connection
	config     *ACF2Config
}

// TopSecretSecuritySystem implements Top Secret security system integration
type TopSecretSecuritySystem struct {
	connection Connection
	config     *TopSecretConfig
}

// Security system configurations
type RACFConfig struct {
	ProfilePrefix   string `json:"profile_prefix"`
	GroupPrefix     string `json:"group_prefix"`
	PermissionClass string `json:"permission_class"`
	DefaultGroup    string `json:"default_group"`
}

type ACF2Config struct {
	LogonID         string `json:"logon_id"`
	DefaultLID      string `json:"default_lid"`
	SecurityLevel   string `json:"security_level"`
}

type TopSecretConfig struct {
	ACID            string `json:"acid"`
	Department      string `json:"department"`
	SecurityLevel   string `json:"security_level"`
}

// NewSecurityManager creates a new security manager
func NewSecurityManager(config *AdapterConfig) *SecurityManager {
	sm := &SecurityManager{
		config: config,
	}
	
	// Initialize encryption
	if err := sm.initializeEncryption(); err != nil {
		log.Printf("Warning: failed to initialize encryption: %v", err)
	}
	
	return sm
}

// initializeEncryption initializes AES encryption for credential storage
func (sm *SecurityManager) initializeEncryption() error {
	if sm.config.EncryptionKey == "" {
		return fmt.Errorf("encryption key not configured")
	}
	
	// Derive key from password using PBKDF2
	salt := []byte("workflowmaster-mainframe-salt") // In production, use random salt
	sm.encryptionKey = pbkdf2.Key([]byte(sm.config.EncryptionKey), salt, 10000, 32, sha256.New)
	
	// Create AES cipher
	block, err := aes.NewCipher(sm.encryptionKey)
	if err != nil {
		return fmt.Errorf("failed to create AES cipher: %w", err)
	}
	
	// Create GCM mode
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return fmt.Errorf("failed to create GCM: %w", err)
	}
	
	sm.gcm = gcm
	return nil
}

// EncryptConnectionCredentials encrypts sensitive connection data
func (sm *SecurityManager) EncryptConnectionCredentials(connection *MainframeConnection) error {
	if sm.gcm == nil {
		return fmt.Errorf("encryption not initialized")
	}
	
	// Encrypt password
	if connection.PasswordEncrypted != "" {
		encrypted, err := sm.encrypt(connection.PasswordEncrypted)
		if err != nil {
			return fmt.Errorf("failed to encrypt password: %w", err)
		}
		connection.PasswordEncrypted = encrypted
	}
	
	return nil
}

// DecryptConnectionCredentials decrypts sensitive connection data
func (sm *SecurityManager) DecryptConnectionCredentials(connection *MainframeConnection) error {
	if sm.gcm == nil {
		return fmt.Errorf("encryption not initialized")
	}
	
	// Decrypt password
	if connection.PasswordEncrypted != "" {
		decrypted, err := sm.decrypt(connection.PasswordEncrypted)
		if err != nil {
			return fmt.Errorf("failed to decrypt password: %w", err)
		}
		connection.PasswordEncrypted = decrypted
	}
	
	return nil
}

// encrypt encrypts a string using AES-GCM
func (sm *SecurityManager) encrypt(plaintext string) (string, error) {
	// Generate random nonce
	nonce := make([]byte, sm.gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return "", fmt.Errorf("failed to generate nonce: %w", err)
	}
	
	// Encrypt the data
	ciphertext := sm.gcm.Seal(nonce, nonce, []byte(plaintext), nil)
	
	// Encode to base64
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// decrypt decrypts a string using AES-GCM
func (sm *SecurityManager) decrypt(ciphertext string) (string, error) {
	// Decode from base64
	data, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return "", fmt.Errorf("failed to decode base64: %w", err)
	}
	
	// Extract nonce
	nonceSize := sm.gcm.NonceSize()
	if len(data) < nonceSize {
		return "", fmt.Errorf("ciphertext too short")
	}
	
	nonce, cipherData := data[:nonceSize], data[nonceSize:]
	
	// Decrypt the data
	plaintext, err := sm.gcm.Open(nil, nonce, cipherData, nil)
	if err != nil {
		return "", fmt.Errorf("failed to decrypt: %w", err)
	}
	
	return string(plaintext), nil
}

// CreateSecuritySystem creates a security system instance based on type
func (sm *SecurityManager) CreateSecuritySystem(systemType string, connection Connection) (SecuritySystem, error) {
	switch strings.ToUpper(systemType) {
	case "RACF":
		return &RACFSecuritySystem{
			connection: connection,
			config: &RACFConfig{
				ProfilePrefix:   "SYS1",
				GroupPrefix:     "GRP",
				PermissionClass: "DATASET",
				DefaultGroup:    "USERS",
			},
		}, nil
	case "ACF2":
		return &ACF2SecuritySystem{
			connection: connection,
			config: &ACF2Config{
				LogonID:       "ACF2",
				DefaultLID:    "USER",
				SecurityLevel: "1",
			},
		}, nil
	case "TOPSECRET", "TSS":
		return &TopSecretSecuritySystem{
			connection: connection,
			config: &TopSecretConfig{
				ACID:          "TSS",
				Department:    "IT",
				SecurityLevel: "1",
			},
		}, nil
	default:
		return nil, fmt.Errorf("unsupported security system: %s", systemType)
	}
}

// RACF Security System Implementation

// Authenticate authenticates a user with RACF
func (racf *RACFSecuritySystem) Authenticate(username, password string) (*SecurityContext, error) {
	// Execute RACF authentication command
	command := fmt.Sprintf("LISTUSER %s", username)
	output, err := racf.connection.ExecuteCommand(command)
	if err != nil {
		return nil, fmt.Errorf("RACF authentication failed: %w", err)
	}
	
	// Parse RACF output to extract user information
	context, err := racf.parseUserInfo(output, username)
	if err != nil {
		return nil, fmt.Errorf("failed to parse RACF user info: %w", err)
	}
	
	return context, nil
}

// Authorize checks if a user is authorized for a resource/action
func (racf *RACFSecuritySystem) Authorize(context *SecurityContext, resource string, action string) (bool, error) {
	// Check RACF permissions
	command := fmt.Sprintf("RLIST %s %s", racf.config.PermissionClass, resource)
	output, err := racf.connection.ExecuteCommand(command)
	if err != nil {
		return false, fmt.Errorf("RACF authorization check failed: %w", err)
	}
	
	// Parse output to check permissions
	return racf.parsePermissions(output, context.Username, action), nil
}

// GetUserProfile retrieves a user profile from RACF
func (racf *RACFSecuritySystem) GetUserProfile(username string) (*UserProfile, error) {
	command := fmt.Sprintf("LISTUSER %s", username)
	output, err := racf.connection.ExecuteCommand(command)
	if err != nil {
		return nil, fmt.Errorf("failed to get RACF user profile: %w", err)
	}
	
	return racf.parseUserProfile(output, username)
}

// ValidatePassword validates a user's password
func (racf *RACFSecuritySystem) ValidatePassword(username, password string) error {
	// In a real implementation, this would use RACF password validation
	// For security reasons, we don't actually validate passwords here
	return nil
}

// ChangePassword changes a user's password
func (racf *RACFSecuritySystem) ChangePassword(username, oldPassword, newPassword string) error {
	command := fmt.Sprintf("ALTUSER %s PASSWORD(%s)", username, newPassword)
	_, err := racf.connection.ExecuteCommand(command)
	if err != nil {
		return fmt.Errorf("RACF password change failed: %w", err)
	}
	
	return nil
}

// GetSecurityGroups retrieves security groups for a user
func (racf *RACFSecuritySystem) GetSecurityGroups(username string) ([]string, error) {
	command := fmt.Sprintf("LISTUSER %s", username)
	output, err := racf.connection.ExecuteCommand(command)
	if err != nil {
		return nil, fmt.Errorf("failed to get RACF groups: %w", err)
	}
	
	return racf.parseGroups(output), nil
}

// parseUserInfo parses RACF user information
func (racf *RACFSecuritySystem) parseUserInfo(output, username string) (*SecurityContext, error) {
	// Simplified parsing - in practice, this would be more sophisticated
	context := &SecurityContext{
		UserID:         username,
		Username:       username,
		SecuritySystem: "RACF",
		Groups:         []string{},
		Permissions:    []string{},
		SessionID:      fmt.Sprintf("racf-%s-%d", username, time.Now().Unix()),
		ExpiresAt:      time.Now().Add(8 * time.Hour),
		Attributes:     make(map[string]interface{}),
	}
	
	// Parse groups and permissions from output
	lines := strings.Split(output, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.Contains(line, "GROUP=") {
			// Extract group information
			parts := strings.Split(line, "GROUP=")
			if len(parts) > 1 {
				group := strings.Fields(parts[1])[0]
				context.Groups = append(context.Groups, group)
			}
		}
	}
	
	return context, nil
}

// parsePermissions parses RACF permissions
func (racf *RACFSecuritySystem) parsePermissions(output, username, action string) bool {
	// Simplified permission checking
	lines := strings.Split(output, "\n")
	for _, line := range lines {
		if strings.Contains(line, username) && strings.Contains(line, strings.ToUpper(action)) {
			return true
		}
	}
	return false
}

// parseUserProfile parses RACF user profile
func (racf *RACFSecuritySystem) parseUserProfile(output, username string) (*UserProfile, error) {
	profile := &UserProfile{
		UserID:      username,
		Username:    username,
		Groups:      []string{},
		Permissions: []string{},
		Attributes:  make(map[string]interface{}),
	}
	
	// Parse profile information from RACF output
	lines := strings.Split(output, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.Contains(line, "NAME=") {
			parts := strings.Split(line, "NAME=")
			if len(parts) > 1 {
				profile.FullName = strings.Trim(parts[1], " '\"")
			}
		}
	}
	
	return profile, nil
}

// parseGroups parses RACF groups
func (racf *RACFSecuritySystem) parseGroups(output string) []string {
	var groups []string
	lines := strings.Split(output, "\n")
	
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.Contains(line, "GROUP=") {
			parts := strings.Split(line, "GROUP=")
			if len(parts) > 1 {
				group := strings.Fields(parts[1])[0]
				groups = append(groups, group)
			}
		}
	}
	
	return groups
}

// ACF2 Security System Implementation (simplified)

func (acf2 *ACF2SecuritySystem) Authenticate(username, password string) (*SecurityContext, error) {
	command := fmt.Sprintf("LIST %s", username)
	output, err := acf2.connection.ExecuteCommand(command)
	if err != nil {
		return nil, fmt.Errorf("ACF2 authentication failed: %w", err)
	}
	
	return &SecurityContext{
		UserID:         username,
		Username:       username,
		SecuritySystem: "ACF2",
		SessionID:      fmt.Sprintf("acf2-%s-%d", username, time.Now().Unix()),
		ExpiresAt:      time.Now().Add(8 * time.Hour),
		Attributes:     make(map[string]interface{}),
	}, nil
}

func (acf2 *ACF2SecuritySystem) Authorize(context *SecurityContext, resource string, action string) (bool, error) {
	// Simplified ACF2 authorization
	return true, nil
}

func (acf2 *ACF2SecuritySystem) GetUserProfile(username string) (*UserProfile, error) {
	return &UserProfile{
		UserID:     username,
		Username:   username,
		Attributes: make(map[string]interface{}),
	}, nil
}

func (acf2 *ACF2SecuritySystem) ValidatePassword(username, password string) error {
	return nil
}

func (acf2 *ACF2SecuritySystem) ChangePassword(username, oldPassword, newPassword string) error {
	return nil
}

func (acf2 *ACF2SecuritySystem) GetSecurityGroups(username string) ([]string, error) {
	return []string{}, nil
}

// Top Secret Security System Implementation (simplified)

func (tss *TopSecretSecuritySystem) Authenticate(username, password string) (*SecurityContext, error) {
	command := fmt.Sprintf("TSS LIST(%s)", username)
	output, err := tss.connection.ExecuteCommand(command)
	if err != nil {
		return nil, fmt.Errorf("Top Secret authentication failed: %w", err)
	}
	
	return &SecurityContext{
		UserID:         username,
		Username:       username,
		SecuritySystem: "TopSecret",
		SessionID:      fmt.Sprintf("tss-%s-%d", username, time.Now().Unix()),
		ExpiresAt:      time.Now().Add(8 * time.Hour),
		Attributes:     make(map[string]interface{}),
	}, nil
}

func (tss *TopSecretSecuritySystem) Authorize(context *SecurityContext, resource string, action string) (bool, error) {
	// Simplified Top Secret authorization
	return true, nil
}

func (tss *TopSecretSecuritySystem) GetUserProfile(username string) (*UserProfile, error) {
	return &UserProfile{
		UserID:     username,
		Username:   username,
		Attributes: make(map[string]interface{}),
	}, nil
}

func (tss *TopSecretSecuritySystem) ValidatePassword(username, password string) error {
	return nil
}

func (tss *TopSecretSecuritySystem) ChangePassword(username, oldPassword, newPassword string) error {
	return nil
}

func (tss *TopSecretSecuritySystem) GetSecurityGroups(username string) ([]string, error) {
	return []string{}, nil
}
