package scheduler

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/robfig/cron/v3"
	"github.com/workflowmaster/workflowmaster/internal/database"
	"github.com/workflowmaster/workflowmaster/internal/models"
	"gorm.io/gorm"
)

// Scheduler manages job scheduling and execution
type Scheduler struct {
	db          *database.Database
	cron        *cron.Cron
	jobs        map[uuid.UUID]*ScheduledJob
	mutex       sync.RWMutex
	ctx         context.Context
	cancel      context.CancelFunc
	eventChan   chan *models.Event
	jobQueue    chan *models.JobInstance
	running     bool
}

// ScheduledJob represents a scheduled job with its cron entry
type ScheduledJob struct {
	Schedule      *models.Schedule
	JobDefinition *models.JobDefinition
	CronEntryID   cron.EntryID
	NextRun       time.Time
	LastRun       *time.Time
}

// NewScheduler creates a new scheduler instance
func NewScheduler(db *database.Database) *Scheduler {
	ctx, cancel := context.WithCancel(context.Background())
	
	// Create cron scheduler with second precision
	cronScheduler := cron.New(cron.WithSeconds())
	
	return &Scheduler{
		db:        db,
		cron:      cronScheduler,
		jobs:      make(map[uuid.UUID]*ScheduledJob),
		ctx:       ctx,
		cancel:    cancel,
		eventChan: make(chan *models.Event, 1000),
		jobQueue:  make(chan *models.JobInstance, 10000),
		running:   false,
	}
}

// Start starts the scheduler
func (s *Scheduler) Start() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if s.running {
		return fmt.Errorf("scheduler is already running")
	}

	log.Println("Starting scheduler...")

	// Load existing schedules from database
	if err := s.loadSchedules(); err != nil {
		return fmt.Errorf("failed to load schedules: %w", err)
	}

	// Start cron scheduler
	s.cron.Start()

	// Start event processor
	go s.processEvents()

	// Start job queue processor
	go s.processJobQueue()

	// Start schedule monitor
	go s.monitorSchedules()

	s.running = true
	log.Println("Scheduler started successfully")

	return nil
}

// Stop stops the scheduler
func (s *Scheduler) Stop() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if !s.running {
		return fmt.Errorf("scheduler is not running")
	}

	log.Println("Stopping scheduler...")

	// Stop cron scheduler
	s.cron.Stop()

	// Cancel context to stop goroutines
	s.cancel()

	// Clear jobs
	s.jobs = make(map[uuid.UUID]*ScheduledJob)

	s.running = false
	log.Println("Scheduler stopped successfully")

	return nil
}

// AddSchedule adds a new schedule to the scheduler
func (s *Scheduler) AddSchedule(schedule *models.Schedule) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	// Load job definition
	var jobDef models.JobDefinition
	if err := s.db.DB.First(&jobDef, schedule.JobDefinitionID).Error; err != nil {
		return fmt.Errorf("failed to load job definition: %w", err)
	}

	// Parse cron expression
	if schedule.CronExpression == nil || *schedule.CronExpression == "" {
		return fmt.Errorf("cron expression is required")
	}

	// Add to cron scheduler
	entryID, err := s.cron.AddFunc(*schedule.CronExpression, func() {
		s.triggerScheduledJob(schedule.ID)
	})
	if err != nil {
		return fmt.Errorf("failed to add cron job: %w", err)
	}

	// Create scheduled job
	scheduledJob := &ScheduledJob{
		Schedule:      schedule,
		JobDefinition: &jobDef,
		CronEntryID:   entryID,
		NextRun:       s.cron.Entry(entryID).Next,
	}

	s.jobs[schedule.ID] = scheduledJob

	log.Printf("Added schedule: %s (ID: %s)", schedule.Name, schedule.ID)
	return nil
}

// RemoveSchedule removes a schedule from the scheduler
func (s *Scheduler) RemoveSchedule(scheduleID uuid.UUID) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	scheduledJob, exists := s.jobs[scheduleID]
	if !exists {
		return fmt.Errorf("schedule not found: %s", scheduleID)
	}

	// Remove from cron scheduler
	s.cron.Remove(scheduledJob.CronEntryID)

	// Remove from jobs map
	delete(s.jobs, scheduleID)

	log.Printf("Removed schedule: %s", scheduleID)
	return nil
}

// UpdateSchedule updates an existing schedule
func (s *Scheduler) UpdateSchedule(schedule *models.Schedule) error {
	// Remove existing schedule
	if err := s.RemoveSchedule(schedule.ID); err != nil {
		log.Printf("Warning: failed to remove existing schedule: %v", err)
	}

	// Add updated schedule
	return s.AddSchedule(schedule)
}

// TriggerJob manually triggers a job
func (s *Scheduler) TriggerJob(jobDefID uuid.UUID, parameters map[string]interface{}) (*models.JobInstance, error) {
	// Load job definition
	var jobDef models.JobDefinition
	if err := s.db.DB.First(&jobDef, jobDefID).Error; err != nil {
		return nil, fmt.Errorf("failed to load job definition: %w", err)
	}

	// Create job instance
	jobInstance := &models.JobInstance{
		JobDefinitionID:      &jobDefID,
		Status:               models.JobStatusPending,
		Priority:             100,
		ScheduledAt:          time.Now().UTC(),
		Parameters:           parameters,
		EnvironmentVariables: jobDef.EnvironmentVariables,
	}

	// Save to database
	if err := s.db.DB.Create(jobInstance).Error; err != nil {
		return nil, fmt.Errorf("failed to create job instance: %w", err)
	}

	// Add to job queue
	select {
	case s.jobQueue <- jobInstance:
		log.Printf("Manually triggered job: %s (Instance: %s)", jobDef.Name, jobInstance.ID)
	default:
		return nil, fmt.Errorf("job queue is full")
	}

	return jobInstance, nil
}

// ProcessEvent processes an incoming event
func (s *Scheduler) ProcessEvent(event *models.Event) error {
	select {
	case s.eventChan <- event:
		return nil
	default:
		return fmt.Errorf("event channel is full")
	}
}

// GetJobQueue returns the job queue channel for external processors
func (s *Scheduler) GetJobQueue() <-chan *models.JobInstance {
	return s.jobQueue
}

// GetScheduledJobs returns all currently scheduled jobs
func (s *Scheduler) GetScheduledJobs() map[uuid.UUID]*ScheduledJob {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	// Create a copy to avoid race conditions
	jobs := make(map[uuid.UUID]*ScheduledJob)
	for id, job := range s.jobs {
		jobs[id] = job
	}
	return jobs
}

// loadSchedules loads all active schedules from the database
func (s *Scheduler) loadSchedules() error {
	var schedules []models.Schedule
	err := s.db.DB.Where("is_active = ?", true).
		Preload("JobDefinition").
		Find(&schedules).Error
	if err != nil {
		return err
	}

	for _, schedule := range schedules {
		if err := s.AddSchedule(&schedule); err != nil {
			log.Printf("Failed to add schedule %s: %v", schedule.Name, err)
		}
	}

	log.Printf("Loaded %d active schedules", len(schedules))
	return nil
}

// triggerScheduledJob triggers a job based on schedule
func (s *Scheduler) triggerScheduledJob(scheduleID uuid.UUID) {
	s.mutex.RLock()
	scheduledJob, exists := s.jobs[scheduleID]
	s.mutex.RUnlock()

	if !exists {
		log.Printf("Schedule not found: %s", scheduleID)
		return
	}

	// Check if schedule is still active
	var schedule models.Schedule
	if err := s.db.DB.First(&schedule, scheduleID).Error; err != nil {
		log.Printf("Failed to load schedule %s: %v", scheduleID, err)
		return
	}

	if !schedule.IsActive {
		log.Printf("Schedule %s is no longer active", scheduleID)
		return
	}

	// Check overlap policy
	if err := s.checkOverlapPolicy(&schedule); err != nil {
		log.Printf("Overlap policy check failed for schedule %s: %v", scheduleID, err)
		return
	}

	// Create job instance
	jobInstance := &models.JobInstance{
		JobDefinitionID:      &scheduledJob.JobDefinition.ID,
		ScheduleID:           &scheduleID,
		Status:               models.JobStatusPending,
		Priority:             100,
		ScheduledAt:          time.Now().UTC(),
		Parameters:           scheduledJob.JobDefinition.Parameters,
		EnvironmentVariables: scheduledJob.JobDefinition.EnvironmentVariables,
	}

	// Save to database
	if err := s.db.DB.Create(jobInstance).Error; err != nil {
		log.Printf("Failed to create job instance for schedule %s: %v", scheduleID, err)
		return
	}

	// Add to job queue
	select {
	case s.jobQueue <- jobInstance:
		log.Printf("Scheduled job triggered: %s (Instance: %s)", 
			scheduledJob.JobDefinition.Name, jobInstance.ID)
		
		// Update last run time
		s.mutex.Lock()
		now := time.Now().UTC()
		scheduledJob.LastRun = &now
		s.mutex.Unlock()
	default:
		log.Printf("Job queue is full, dropping job instance: %s", jobInstance.ID)
	}
}

// checkOverlapPolicy checks if a job can be scheduled based on overlap policy
func (s *Scheduler) checkOverlapPolicy(schedule *models.Schedule) error {
	// Count running instances for this schedule
	var runningCount int64
	err := s.db.DB.Model(&models.JobInstance{}).
		Where("schedule_id = ? AND status IN (?)", 
			schedule.ID, []string{models.JobStatusPending, models.JobStatusRunning}).
		Count(&runningCount).Error
	if err != nil {
		return fmt.Errorf("failed to count running instances: %w", err)
	}

	switch schedule.OverlapPolicy {
	case "skip":
		if runningCount > 0 {
			return fmt.Errorf("skipping due to overlap policy: %d instances running", runningCount)
		}
	case "queue":
		if runningCount >= int64(schedule.MaxInstances) {
			return fmt.Errorf("queue full: %d instances running (max: %d)", 
				runningCount, schedule.MaxInstances)
		}
	case "replace":
		// Cancel running instances
		if runningCount > 0 {
			err := s.db.DB.Model(&models.JobInstance{}).
				Where("schedule_id = ? AND status IN (?)", 
					schedule.ID, []string{models.JobStatusPending, models.JobStatusRunning}).
				Update("status", models.JobStatusCancelled).Error
			if err != nil {
				return fmt.Errorf("failed to cancel running instances: %w", err)
			}
			log.Printf("Cancelled %d running instances for schedule %s", runningCount, schedule.ID)
		}
	}

	return nil
}

// processEvents processes incoming events and triggers jobs
func (s *Scheduler) processEvents() {
	for {
		select {
		case <-s.ctx.Done():
			return
		case event := <-s.eventChan:
			s.handleEvent(event)
		}
	}
}

// processJobQueue processes the job queue (placeholder for executor integration)
func (s *Scheduler) processJobQueue() {
	for {
		select {
		case <-s.ctx.Done():
			return
		case jobInstance := <-s.jobQueue:
			// This will be handled by the executor service
			log.Printf("Job queued for execution: %s", jobInstance.ID)
		}
	}
}

// monitorSchedules periodically checks and updates schedule information
func (s *Scheduler) monitorSchedules() {
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-s.ctx.Done():
			return
		case <-ticker.C:
			s.updateScheduleInfo()
		}
	}
}

// handleEvent processes a single event and triggers matching jobs
func (s *Scheduler) handleEvent(event *models.Event) {
	// Find event triggers that match this event
	var triggers []models.EventTrigger
	err := s.db.DB.Where("event_type = ? AND is_active = ?",
		event.EventType, true).
		Preload("JobDefinition").
		Find(&triggers).Error
	if err != nil {
		log.Printf("Failed to find event triggers: %v", err)
		return
	}

	for _, trigger := range triggers {
		// Check condition expression if present
		if trigger.ConditionExpression != nil && *trigger.ConditionExpression != "" {
			// TODO: Implement condition expression evaluation
			// For now, we'll trigger all matching events
		}

		// Create job instance
		jobInstance := &models.JobInstance{
			JobDefinitionID:      &trigger.JobDefinitionID,
			Status:               models.JobStatusPending,
			Priority:             100,
			ScheduledAt:          time.Now().UTC(),
			Parameters:           event.Payload,
			EnvironmentVariables: trigger.JobDefinition.EnvironmentVariables,
		}

		// Save to database
		if err := s.db.DB.Create(jobInstance).Error; err != nil {
			log.Printf("Failed to create job instance for event trigger: %v", err)
			continue
		}

		// Add to job queue
		select {
		case s.jobQueue <- jobInstance:
			log.Printf("Event-triggered job queued: %s (Event: %s)",
				trigger.JobDefinition.Name, event.EventType)
		default:
			log.Printf("Job queue is full, dropping event-triggered job: %s", jobInstance.ID)
		}
	}

	// Mark event as processed
	event.Processed = true
	now := time.Now().UTC()
	event.ProcessedAt = &now
	s.db.DB.Save(event)
}

// updateScheduleInfo updates next run times and other schedule information
func (s *Scheduler) updateScheduleInfo() {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	for _, scheduledJob := range s.jobs {
		// Update next run time from cron
		entry := s.cron.Entry(scheduledJob.CronEntryID)
		scheduledJob.NextRun = entry.Next
	}
}

// IsRunning returns whether the scheduler is currently running
func (s *Scheduler) IsRunning() bool {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.running
}

// GetStats returns scheduler statistics
func (s *Scheduler) GetStats() map[string]interface{} {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	stats := map[string]interface{}{
		"running":         s.running,
		"scheduled_jobs":  len(s.jobs),
		"queue_length":    len(s.jobQueue),
		"event_queue":     len(s.eventChan),
	}

	return stats
}
