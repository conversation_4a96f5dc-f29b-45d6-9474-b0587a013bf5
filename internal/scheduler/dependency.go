package scheduler

import (
	"fmt"
	"log"
	"time"

	"github.com/google/uuid"
	"github.com/workflowmaster/workflowmaster/internal/database"
	"github.com/workflowmaster/workflowmaster/internal/models"
)

// DependencyManager handles job dependency resolution and execution ordering
type DependencyManager struct {
	db *database.Database
}

// NewDependencyManager creates a new dependency manager
func NewDependencyManager(db *database.Database) *DependencyManager {
	return &DependencyManager{
		db: db,
	}
}

// CheckDependencies checks if all dependencies for a job are satisfied
func (dm *DependencyManager) CheckDependencies(jobInstanceID uuid.UUID) (bool, error) {
	// Get the job instance
	var jobInstance models.JobInstance
	err := dm.db.DB.Preload("JobDefinition").First(&jobInstance, jobInstanceID).Error
	if err != nil {
		return false, fmt.Errorf("failed to load job instance: %w", err)
	}

	if jobInstance.JobDefinitionID == nil {
		return true, nil // No job definition, no dependencies
	}

	// Get all dependencies for this job
	var dependencies []models.JobDependency
	err = dm.db.DB.Where("child_job_id = ?", *jobInstance.JobDefinitionID).
		Preload("ParentJob").
		Find(&dependencies).Error
	if err != nil {
		return false, fmt.Errorf("failed to load dependencies: %w", err)
	}

	if len(dependencies) == 0 {
		return true, nil // No dependencies
	}

	// Check each dependency
	for _, dep := range dependencies {
		satisfied, err := dm.isDependencySatisfied(&dep, &jobInstance)
		if err != nil {
			return false, fmt.Errorf("failed to check dependency %s: %w", dep.ID, err)
		}
		if !satisfied {
			log.Printf("Dependency not satisfied for job %s: parent job %s (%s)", 
				jobInstance.ID, dep.ParentJob.Name, dep.DependencyType)
			return false, nil
		}
	}

	return true, nil
}

// isDependencySatisfied checks if a specific dependency is satisfied
func (dm *DependencyManager) isDependencySatisfied(dependency *models.JobDependency, childInstance *models.JobInstance) (bool, error) {
	// Find the most recent instance of the parent job that completed before this child was scheduled
	var parentInstance models.JobInstance
	query := dm.db.DB.Where("job_definition_id = ? AND completed_at IS NOT NULL AND completed_at <= ?", 
		dependency.ParentJobID, childInstance.ScheduledAt).
		Order("completed_at DESC")

	// If this is part of a workflow, only consider instances from the same workflow
	if childInstance.WorkflowInstanceID != nil {
		query = query.Where("workflow_instance_id = ?", *childInstance.WorkflowInstanceID)
	}

	err := query.First(&parentInstance).Error
	if err != nil {
		// No completed parent instance found
		return false, nil
	}

	// Check dependency type
	switch dependency.DependencyType {
	case models.DependencyTypeSuccess:
		return parentInstance.Status == models.JobStatusSuccess, nil
	case models.DependencyTypeFailure:
		return parentInstance.Status == models.JobStatusFailed, nil
	case models.DependencyTypeCompletion:
		return parentInstance.Status == models.JobStatusSuccess || 
			   parentInstance.Status == models.JobStatusFailed, nil
	case models.DependencyTypeAlways:
		return true, nil
	default:
		return false, fmt.Errorf("unknown dependency type: %s", dependency.DependencyType)
	}
}

// GetReadyJobs returns all job instances that are ready to run (dependencies satisfied)
func (dm *DependencyManager) GetReadyJobs() ([]*models.JobInstance, error) {
	// Get all pending job instances
	var pendingJobs []models.JobInstance
	err := dm.db.DB.Where("status = ?", models.JobStatusPending).
		Preload("JobDefinition").
		Order("priority DESC, scheduled_at ASC").
		Find(&pendingJobs).Error
	if err != nil {
		return nil, fmt.Errorf("failed to load pending jobs: %w", err)
	}

	var readyJobs []*models.JobInstance
	for i := range pendingJobs {
		job := &pendingJobs[i]
		ready, err := dm.CheckDependencies(job.ID)
		if err != nil {
			log.Printf("Error checking dependencies for job %s: %v", job.ID, err)
			continue
		}
		if ready {
			readyJobs = append(readyJobs, job)
		}
	}

	return readyJobs, nil
}

// TriggerDependentJobs triggers jobs that depend on the completed job
func (dm *DependencyManager) TriggerDependentJobs(completedJobInstance *models.JobInstance) error {
	if completedJobInstance.JobDefinitionID == nil {
		return nil // No job definition, no dependents
	}

	// Find all jobs that depend on this job
	var dependencies []models.JobDependency
	err := dm.db.DB.Where("parent_job_id = ?", *completedJobInstance.JobDefinitionID).
		Preload("ChildJob").
		Find(&dependencies).Error
	if err != nil {
		return fmt.Errorf("failed to load dependent jobs: %w", err)
	}

	for _, dep := range dependencies {
		// Check if there are any pending instances of the dependent job
		var pendingInstances []models.JobInstance
		query := dm.db.DB.Where("job_definition_id = ? AND status = ?", 
			dep.ChildJobID, models.JobStatusPending)

		// If this is part of a workflow, only consider instances from the same workflow
		if completedJobInstance.WorkflowInstanceID != nil {
			query = query.Where("workflow_instance_id = ?", *completedJobInstance.WorkflowInstanceID)
		}

		err := query.Find(&pendingInstances).Error
		if err != nil {
			log.Printf("Error finding pending instances for dependent job %s: %v", dep.ChildJobID, err)
			continue
		}

		// Check if any of these pending instances are now ready
		for _, instance := range pendingInstances {
			ready, err := dm.CheckDependencies(instance.ID)
			if err != nil {
				log.Printf("Error checking dependencies for job %s: %v", instance.ID, err)
				continue
			}
			if ready {
				log.Printf("Job %s is now ready due to completion of %s", 
					instance.ID, completedJobInstance.ID)
				// The scheduler will pick this up in the next cycle
			}
		}
	}

	return nil
}

// ValidateDependencies validates that job dependencies don't create cycles
func (dm *DependencyManager) ValidateDependencies(jobDefID uuid.UUID) error {
	visited := make(map[uuid.UUID]bool)
	recursionStack := make(map[uuid.UUID]bool)

	return dm.detectCycle(jobDefID, visited, recursionStack)
}

// detectCycle performs DFS to detect cycles in the dependency graph
func (dm *DependencyManager) detectCycle(jobDefID uuid.UUID, visited, recursionStack map[uuid.UUID]bool) error {
	visited[jobDefID] = true
	recursionStack[jobDefID] = true

	// Get all jobs that this job depends on (parent jobs)
	var dependencies []models.JobDependency
	err := dm.db.DB.Where("child_job_id = ?", jobDefID).Find(&dependencies).Error
	if err != nil {
		return fmt.Errorf("failed to load dependencies: %w", err)
	}

	for _, dep := range dependencies {
		parentID := dep.ParentJobID

		// If parent is not visited, recursively check it
		if !visited[parentID] {
			if err := dm.detectCycle(parentID, visited, recursionStack); err != nil {
				return err
			}
		} else if recursionStack[parentID] {
			// If parent is in recursion stack, we found a cycle
			return fmt.Errorf("circular dependency detected involving job %s", parentID)
		}
	}

	recursionStack[jobDefID] = false
	return nil
}

// GetDependencyGraph returns the dependency graph for visualization
func (dm *DependencyManager) GetDependencyGraph(workflowID *uuid.UUID) (map[string]interface{}, error) {
	var dependencies []models.JobDependency
	query := dm.db.DB.Preload("ParentJob").Preload("ChildJob")

	if workflowID != nil {
		// Get dependencies for jobs in a specific workflow
		var workflow models.Workflow
		err := dm.db.DB.First(&workflow, *workflowID).Error
		if err != nil {
			return nil, fmt.Errorf("workflow not found: %w", err)
		}

		// Extract job IDs from workflow definition
		// This is a simplified approach - in practice, you'd parse the workflow definition
		query = query.Where("parent_job_id IN (SELECT id FROM job_definitions WHERE id IN (?))", 
			[]uuid.UUID{}) // TODO: Extract from workflow definition
	}

	err := query.Find(&dependencies).Error
	if err != nil {
		return nil, fmt.Errorf("failed to load dependencies: %w", err)
	}

	// Build graph structure
	nodes := make(map[uuid.UUID]map[string]interface{})
	edges := make([]map[string]interface{}, 0)

	for _, dep := range dependencies {
		// Add parent node
		if _, exists := nodes[dep.ParentJobID]; !exists {
			nodes[dep.ParentJobID] = map[string]interface{}{
				"id":   dep.ParentJobID,
				"name": dep.ParentJob.Name,
				"type": dep.ParentJob.JobType,
			}
		}

		// Add child node
		if _, exists := nodes[dep.ChildJobID]; !exists {
			nodes[dep.ChildJobID] = map[string]interface{}{
				"id":   dep.ChildJobID,
				"name": dep.ChildJob.Name,
				"type": dep.ChildJob.JobType,
			}
		}

		// Add edge
		edges = append(edges, map[string]interface{}{
			"from":            dep.ParentJobID,
			"to":              dep.ChildJobID,
			"dependency_type": dep.DependencyType,
			"condition":       dep.ConditionExpression,
		})
	}

	// Convert nodes map to slice
	nodeSlice := make([]map[string]interface{}, 0, len(nodes))
	for _, node := range nodes {
		nodeSlice = append(nodeSlice, node)
	}

	return map[string]interface{}{
		"nodes": nodeSlice,
		"edges": edges,
	}, nil
}

// CreateDependency creates a new job dependency
func (dm *DependencyManager) CreateDependency(parentJobID, childJobID uuid.UUID, dependencyType string) error {
	// Validate dependency type
	validTypes := []string{
		models.DependencyTypeSuccess,
		models.DependencyTypeFailure,
		models.DependencyTypeCompletion,
		models.DependencyTypeAlways,
	}
	
	valid := false
	for _, validType := range validTypes {
		if dependencyType == validType {
			valid = true
			break
		}
	}
	if !valid {
		return fmt.Errorf("invalid dependency type: %s", dependencyType)
	}

	// Check if dependency already exists
	var existingDep models.JobDependency
	err := dm.db.DB.Where("parent_job_id = ? AND child_job_id = ?", 
		parentJobID, childJobID).First(&existingDep).Error
	if err == nil {
		return fmt.Errorf("dependency already exists")
	}

	// Create new dependency
	dependency := &models.JobDependency{
		ParentJobID:    parentJobID,
		ChildJobID:     childJobID,
		DependencyType: dependencyType,
		CreatedAt:      time.Now().UTC(),
	}

	err = dm.db.DB.Create(dependency).Error
	if err != nil {
		return fmt.Errorf("failed to create dependency: %w", err)
	}

	// Validate that this doesn't create a cycle
	if err := dm.ValidateDependencies(childJobID); err != nil {
		// Rollback the dependency creation
		dm.db.DB.Delete(dependency)
		return fmt.Errorf("dependency would create a cycle: %w", err)
	}

	log.Printf("Created dependency: %s -> %s (%s)", parentJobID, childJobID, dependencyType)
	return nil
}
