package executor

import (
	"context"
	"fmt"
	"log"
	"os"
	"os/exec"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/workflowmaster/workflowmaster/internal/database"
	"github.com/workflowmaster/workflowmaster/internal/models"
)

// Executor manages job execution on execution nodes
type Executor struct {
	db              *database.Database
	nodeID          uuid.UUID
	maxConcurrent   int
	currentJobs     map[uuid.UUID]*RunningJob
	mutex           sync.RWMutex
	ctx             context.Context
	cancel          context.CancelFunc
	jobQueue        chan *models.JobInstance
	running         bool
}

// RunningJob represents a currently executing job
type RunningJob struct {
	Instance  *models.JobInstance
	Process   *exec.Cmd
	StartTime time.Time
	Cancel    context.CancelFunc
}

// JobExecutor interface for different job types
type JobExecutor interface {
	Execute(ctx context.Context, job *models.JobInstance) error
	GetType() string
}

// NewExecutor creates a new executor instance
func NewExecutor(db *database.Database, nodeID uuid.UUID, maxConcurrent int) *Executor {
	ctx, cancel := context.WithCancel(context.Background())
	
	return &Executor{
		db:            db,
		nodeID:        nodeID,
		maxConcurrent: maxConcurrent,
		currentJobs:   make(map[uuid.UUID]*RunningJob),
		ctx:           ctx,
		cancel:        cancel,
		jobQueue:      make(chan *models.JobInstance, 1000),
		running:       false,
	}
}

// Start starts the executor
func (e *Executor) Start() error {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	if e.running {
		return fmt.Errorf("executor is already running")
	}

	log.Printf("Starting executor on node %s...", e.nodeID)

	// Register this node as online
	if err := e.registerNode(); err != nil {
		return fmt.Errorf("failed to register node: %w", err)
	}

	// Start job processor
	go e.processJobs()

	// Start heartbeat
	go e.sendHeartbeat()

	// Start job monitor
	go e.monitorJobs()

	e.running = true
	log.Printf("Executor started successfully on node %s", e.nodeID)

	return nil
}

// Stop stops the executor
func (e *Executor) Stop() error {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	if !e.running {
		return fmt.Errorf("executor is not running")
	}

	log.Printf("Stopping executor on node %s...", e.nodeID)

	// Cancel all running jobs
	for _, job := range e.currentJobs {
		if job.Cancel != nil {
			job.Cancel()
		}
	}

	// Cancel context to stop goroutines
	e.cancel()

	// Update node status to offline
	e.db.DB.Model(&models.ExecutionNode{}).
		Where("id = ?", e.nodeID).
		Update("status", "offline")

	e.running = false
	log.Printf("Executor stopped on node %s", e.nodeID)

	return nil
}

// SubmitJob submits a job for execution
func (e *Executor) SubmitJob(job *models.JobInstance) error {
	select {
	case e.jobQueue <- job:
		return nil
	default:
		return fmt.Errorf("job queue is full")
	}
}

// processJobs processes jobs from the queue
func (e *Executor) processJobs() {
	for {
		select {
		case <-e.ctx.Done():
			return
		case job := <-e.jobQueue:
			if e.canExecuteJob() {
				go e.executeJob(job)
			} else {
				// Put job back in queue or handle overflow
				log.Printf("Cannot execute job %s: max concurrent jobs reached", job.ID)
				// TODO: Implement job queuing strategy
			}
		}
	}
}

// canExecuteJob checks if we can execute another job
func (e *Executor) canExecuteJob() bool {
	e.mutex.RLock()
	defer e.mutex.RUnlock()
	return len(e.currentJobs) < e.maxConcurrent
}

// executeJob executes a single job
func (e *Executor) executeJob(job *models.JobInstance) {
	log.Printf("Starting execution of job %s", job.ID)

	// Update job status to running
	job.Status = models.JobStatusRunning
	job.ExecutionNodeID = &e.nodeID
	startTime := time.Now().UTC()
	job.StartedAt = &startTime

	if err := e.db.DB.Save(job).Error; err != nil {
		log.Printf("Failed to update job status: %v", err)
		return
	}

	// Create execution context with timeout
	ctx, cancel := context.WithTimeout(e.ctx, time.Duration(job.JobDefinition.TimeoutSeconds)*time.Second)
	defer cancel()

	// Add to running jobs
	runningJob := &RunningJob{
		Instance:  job,
		StartTime: startTime,
		Cancel:    cancel,
	}

	e.mutex.Lock()
	e.currentJobs[job.ID] = runningJob
	e.mutex.Unlock()

	// Execute the job
	err := e.executeJobByType(ctx, job)

	// Remove from running jobs
	e.mutex.Lock()
	delete(e.currentJobs, job.ID)
	e.mutex.Unlock()

	// Update job completion status
	completedAt := time.Now().UTC()
	job.CompletedAt = &completedAt

	if err != nil {
		job.Status = models.JobStatusFailed
		job.ErrorLog = stringPtr(err.Error())
		log.Printf("Job %s failed: %v", job.ID, err)
	} else {
		job.Status = models.JobStatusSuccess
		log.Printf("Job %s completed successfully", job.ID)
	}

	// Save final status
	if err := e.db.DB.Save(job).Error; err != nil {
		log.Printf("Failed to update job completion status: %v", err)
	}

	// Update node current job count
	e.updateNodeJobCount()
}

// executeJobByType executes a job based on its type
func (e *Executor) executeJobByType(ctx context.Context, job *models.JobInstance) error {
	switch job.JobDefinition.JobType {
	case "shell":
		return e.executeShellJob(ctx, job)
	case "sql":
		return e.executeSQLJob(ctx, job)
	case "http":
		return e.executeHTTPJob(ctx, job)
	case "python":
		return e.executePythonJob(ctx, job)
	case "docker":
		return e.executeDockerJob(ctx, job)
	default:
		return fmt.Errorf("unsupported job type: %s", job.JobDefinition.JobType)
	}
}

// executeShellJob executes a shell command
func (e *Executor) executeShellJob(ctx context.Context, job *models.JobInstance) error {
	cmd := exec.CommandContext(ctx, "sh", "-c", job.JobDefinition.Command)

	// Set working directory
	if job.JobDefinition.WorkingDirectory != nil {
		cmd.Dir = *job.JobDefinition.WorkingDirectory
	}

	// Set environment variables
	env := os.Environ()
	for key, value := range job.JobDefinition.EnvironmentVariables {
		env = append(env, fmt.Sprintf("%s=%s", key, value))
	}
	for key, value := range job.EnvironmentVariables {
		env = append(env, fmt.Sprintf("%s=%s", key, value))
	}
	cmd.Env = env

	// Capture output
	output, err := cmd.CombinedOutput()
	job.OutputLog = stringPtr(string(output))

	if err != nil {
		if exitError, ok := err.(*exec.ExitError); ok {
			job.ExitCode = &exitError.ExitCode()
		}
		return err
	}

	job.ExitCode = intPtr(0)
	return nil
}

// executeSQLJob executes a SQL command
func (e *Executor) executeSQLJob(ctx context.Context, job *models.JobInstance) error {
	// TODO: Implement SQL job execution
	// This would connect to a database and execute SQL commands
	return fmt.Errorf("SQL job execution not implemented yet")
}

// executeHTTPJob executes an HTTP request
func (e *Executor) executeHTTPJob(ctx context.Context, job *models.JobInstance) error {
	// TODO: Implement HTTP job execution
	// This would make HTTP requests based on job configuration
	return fmt.Errorf("HTTP job execution not implemented yet")
}

// executePythonJob executes a Python script
func (e *Executor) executePythonJob(ctx context.Context, job *models.JobInstance) error {
	// Execute Python script using shell executor for now
	pythonCmd := fmt.Sprintf("python3 -c \"%s\"", 
		strings.ReplaceAll(job.JobDefinition.Command, "\"", "\\\""))
	
	// Create a temporary job with the Python command
	tempJob := *job
	tempJob.JobDefinition = &models.JobDefinition{
		Command:              pythonCmd,
		WorkingDirectory:     job.JobDefinition.WorkingDirectory,
		EnvironmentVariables: job.JobDefinition.EnvironmentVariables,
		TimeoutSeconds:       job.JobDefinition.TimeoutSeconds,
	}

	return e.executeShellJob(ctx, &tempJob)
}

// executeDockerJob executes a Docker container
func (e *Executor) executeDockerJob(ctx context.Context, job *models.JobInstance) error {
	// TODO: Implement Docker job execution
	// This would run Docker containers based on job configuration
	return fmt.Errorf("Docker job execution not implemented yet")
}

// registerNode registers this execution node in the database
func (e *Executor) registerNode() error {
	hostname, _ := os.Hostname()
	
	node := &models.ExecutionNode{
		ID:                e.nodeID,
		Name:              fmt.Sprintf("node-%s", e.nodeID.String()[:8]),
		Hostname:          hostname,
		Platform:          "linux", // TODO: Detect actual platform
		Architecture:      "amd64", // TODO: Detect actual architecture
		MaxConcurrentJobs: e.maxConcurrent,
		CurrentJobs:       0,
		Status:            "online",
		LastHeartbeat:     timePtr(time.Now().UTC()),
	}

	return e.db.DB.Save(node).Error
}

// sendHeartbeat sends periodic heartbeats to indicate node is alive
func (e *Executor) sendHeartbeat() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-e.ctx.Done():
			return
		case <-ticker.C:
			now := time.Now().UTC()
			e.db.DB.Model(&models.ExecutionNode{}).
				Where("id = ?", e.nodeID).
				Update("last_heartbeat", now)
		}
	}
}

// monitorJobs monitors running jobs for timeouts and resource usage
func (e *Executor) monitorJobs() {
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-e.ctx.Done():
			return
		case <-ticker.C:
			e.checkJobTimeouts()
		}
	}
}

// checkJobTimeouts checks for and handles job timeouts
func (e *Executor) checkJobTimeouts() {
	e.mutex.RLock()
	defer e.mutex.RUnlock()

	for _, job := range e.currentJobs {
		timeout := time.Duration(job.Instance.JobDefinition.TimeoutSeconds) * time.Second
		if time.Since(job.StartTime) > timeout {
			log.Printf("Job %s timed out, cancelling...", job.Instance.ID)
			if job.Cancel != nil {
				job.Cancel()
			}
			
			// Update job status
			job.Instance.Status = models.JobStatusTimeout
			completedAt := time.Now().UTC()
			job.Instance.CompletedAt = &completedAt
			e.db.DB.Save(job.Instance)
		}
	}
}

// updateNodeJobCount updates the current job count for this node
func (e *Executor) updateNodeJobCount() {
	e.mutex.RLock()
	count := len(e.currentJobs)
	e.mutex.RUnlock()

	e.db.DB.Model(&models.ExecutionNode{}).
		Where("id = ?", e.nodeID).
		Update("current_jobs", count)
}

// GetStats returns executor statistics
func (e *Executor) GetStats() map[string]interface{} {
	e.mutex.RLock()
	defer e.mutex.RUnlock()

	return map[string]interface{}{
		"node_id":        e.nodeID,
		"running":        e.running,
		"current_jobs":   len(e.currentJobs),
		"max_concurrent": e.maxConcurrent,
		"queue_length":   len(e.jobQueue),
	}
}

// Helper functions
func stringPtr(s string) *string {
	return &s
}

func intPtr(i int) *int {
	return &i
}

func timePtr(t time.Time) *time.Time {
	return &t
}
