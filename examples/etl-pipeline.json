{"name": "Daily ETL Pipeline", "description": "Extract, transform, and load daily sales data", "version": "1.0", "jobs": {"extract_sales_data": {"id": "extract_sales_data", "name": "Extract Sales Data", "type": "job", "job_definition_id": "550e8400-e29b-41d4-a716-446655440001", "parameters": {"source_database": "sales_db", "table": "daily_sales", "date": "{{ workflow.parameters.date }}"}, "timeout": 1800, "retry_policy": {"max_attempts": 3, "delay_seconds": 60, "backoff_type": "exponential"}}, "validate_data": {"id": "validate_data", "name": "Validate Extracted Data", "type": "job", "job_definition_id": "550e8400-e29b-41d4-a716-446655440002", "parameters": {"input_file": "{{ jobs.extract_sales_data.output.file_path }}", "validation_rules": ["not_null:customer_id", "positive:amount", "date_format:transaction_date"]}, "condition": "{{ jobs.extract_sales_data.status == 'success' }}", "timeout": 600}, "transform_data": {"id": "transform_data", "name": "Transform Sales Data", "type": "job", "job_definition_id": "550e8400-e29b-41d4-a716-446655440003", "parameters": {"input_file": "{{ jobs.extract_sales_data.output.file_path }}", "transformations": ["normalize_customer_names", "calculate_tax", "convert_currency"], "target_currency": "USD"}, "timeout": 2400}, "load_to_warehouse": {"id": "load_to_warehouse", "name": "Load to Data Warehouse", "type": "job", "job_definition_id": "550e8400-e29b-41d4-a716-446655440004", "parameters": {"input_file": "{{ jobs.transform_data.output.file_path }}", "target_table": "warehouse.sales_fact", "load_mode": "append"}, "timeout": 1800}, "update_aggregates": {"id": "update_aggregates", "name": "Update Aggregate Tables", "type": "job", "job_definition_id": "550e8400-e29b-41d4-a716-446655440005", "parameters": {"date": "{{ workflow.parameters.date }}", "tables": ["daily_sales_summary", "monthly_sales_summary", "customer_metrics"]}, "timeout": 900}, "send_notification": {"id": "send_notification", "name": "Send Completion Notification", "type": "job", "job_definition_id": "550e8400-e29b-41d4-a716-446655440006", "parameters": {"recipients": ["<EMAIL>"], "subject": "ETL Pipeline Completed - {{ workflow.parameters.date }}", "template": "etl_completion", "data": {"date": "{{ workflow.parameters.date }}", "records_processed": "{{ jobs.transform_data.output.record_count }}", "execution_time": "{{ workflow.execution_time }}"}}}, "cleanup_temp_files": {"id": "cleanup_temp_files", "name": "Cleanup Temporary Files", "type": "job", "job_definition_id": "550e8400-e29b-41d4-a716-446655440007", "parameters": {"file_patterns": ["/tmp/etl_{{ workflow.parameters.date }}_*.csv", "/tmp/validation_{{ workflow.parameters.date }}_*.log"]}}}, "edges": [{"from": "extract_sales_data", "to": "validate_data", "type": "success"}, {"from": "extract_sales_data", "to": "transform_data", "type": "success"}, {"from": "validate_data", "to": "transform_data", "type": "success", "condition": "{{ jobs.validate_data.output.validation_passed == true }}"}, {"from": "transform_data", "to": "load_to_warehouse", "type": "success"}, {"from": "load_to_warehouse", "to": "update_aggregates", "type": "success"}, {"from": "update_aggregates", "to": "send_notification", "type": "success"}, {"from": "update_aggregates", "to": "cleanup_temp_files", "type": "completion"}, {"from": "validate_data", "to": "send_notification", "type": "failure", "condition": "{{ jobs.validate_data.output.validation_passed == false }}"}], "parameters": {"date": {"type": "string", "description": "Date to process (YYYY-MM-DD)", "default": "{{ today }}"}, "environment": {"type": "string", "description": "Target environment", "default": "production", "allowed_values": ["development", "staging", "production"]}}, "variables": {"temp_directory": "/tmp/etl_{{ workflow.parameters.date }}", "log_level": "INFO"}, "schedule": {"cron": "0 2 * * *", "timezone": "UTC", "enabled": true}, "sla": {"max_duration_minutes": 120, "notifications": [{"type": "email", "recipients": ["<EMAIL>"], "trigger": "sla_breach"}]}, "error_handling": {"on_failure": {"notify": ["<EMAIL>"], "retry_workflow": false, "cleanup": true}}}