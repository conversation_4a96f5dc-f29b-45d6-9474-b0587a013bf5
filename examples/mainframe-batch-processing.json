{"name": "Mainframe Daily Batch Processing", "description": "Daily batch processing workflow for mainframe systems including data extraction, validation, and reporting", "version": "1.0", "jobs": {"extract_customer_data": {"id": "extract_customer_data", "name": "Extract Customer Data", "type": "mainframe_jcl", "mainframe_config": {"connection_id": "550e8400-e29b-41d4-a716-************", "job_class": "A", "job_type": "JCL", "jcl_template": "//CUSTEXTR JOB CLASS=${JOB_CLASS},MSGCLASS=X,NOTIFY=&SYSUID\n//STEP1    EXEC PGM=CUSTEXTR\n//CUSTFILE DD DSN=${CUSTOMER_DATASET},DISP=SHR\n//OUTFILE  DD DSN=${OUTPUT_DATASET},DISP=(NEW,CATLG,DELETE),\n//            UNIT=SYSDA,SPACE=(CYL,(10,5)),\n//            DCB=(RECFM=FB,LRECL=80,BLKSIZE=8000)\n//SYSIN    DD *\nEXTRACT DATE=${EXTRACT_DATE}\n/*", "jcl_parameters": {"JOB_CLASS": "A", "CUSTOMER_DATASET": "PROD.CUSTOMER.MASTER", "OUTPUT_DATASET": "WORK.CUSTOMER.EXTRACT.D${YYYYMMDD}", "EXTRACT_DATE": "{{ workflow.parameters.business_date }}"}, "dataset_operations": [{"operation": "allocate", "dataset_name": "WORK.CUSTOMER.EXTRACT.D${YYYYMMDD}", "dataset_type": "PS", "record_format": "FB", "record_length": 80, "block_size": 8000, "space": "CYL(10,5)"}], "monitoring_config": {"enable_monitoring": true, "metric_types": ["cpu", "memory", "job_queue"], "collection_interval": "30s"}}, "parameters": {"business_date": "{{ workflow.parameters.business_date }}", "extract_type": "full"}, "timeout": 1800, "retry_policy": {"max_attempts": 3, "delay_seconds": 300, "backoff_type": "exponential"}}, "validate_customer_data": {"id": "validate_customer_data", "name": "Validate Customer Data", "type": "mainframe_cobol", "mainframe_config": {"connection_id": "550e8400-e29b-41d4-a716-************", "job_class": "B", "job_type": "COBOL", "jcl_template": "//CUSTVAL  JOB CLASS=${J<PERSON><PERSON>_CLA<PERSON>},MSGCLASS=X,NOTIFY=&SYSUID\n//COMPILE  EXEC PROC=COBUCG,PARM.COB='FLAGW,LOAD,SUPMAP,SIZE=2048K,BUF=1024K'\n//COB.SYSIN DD DSN=${COBOL_SOURCE},DISP=SHR\n//LKED.SYSLMOD DD DSN=${LOAD_LIBRARY}(CUSTVAL),DISP=SHR\n//GO       EXEC PGM=CUSTVAL\n//STEPLIB  DD DSN=${LOAD_LIBRARY},DISP=SHR\n//INFILE   DD DSN=${INPUT_DATASET},DISP=SHR\n//OUTFILE  DD DSN=${VALID_DATASET},DISP=(NEW,CATLG,DELETE),\n//            UNIT=SYSDA,SPACE=(CYL,(5,2))\n//ERRFILE  DD DSN=${ERROR_DATASET},DISP=(NEW,CATLG,DELETE),\n//            UNIT=SYSDA,SPACE=(CYL,(2,1))\n//SYSOUT   DD SYSOUT=*", "jcl_parameters": {"JOB_CLASS": "B", "COBOL_SOURCE": "PROD.SOURCE.COBOL(CUSTVAL)", "LOAD_LIBRARY": "PROD.LOAD.LIBRARY", "INPUT_DATASET": "WORK.CUSTOMER.EXTRACT.D${YYYYMMDD}", "VALID_DATASET": "WORK.CUSTOMER.VALID.D${YYYYMMDD}", "ERROR_DATASET": "WORK.CUSTOMER.ERRORS.D${YYYYMMDD}"}, "notification_rules": [{"trigger": "on_failure", "notification_type": "email", "recipients": ["<EMAIL>"], "template": "validation_failure", "parameters": {"dataset": "WORK.CUSTOMER.EXTRACT.D${YYYYMMDD}"}}]}, "condition": "{{ jobs.extract_customer_data.status == 'success' }}", "timeout": 1200}, "transfer_to_warehouse": {"id": "transfer_to_warehouse", "name": "Transfer Data to Warehouse", "type": "mainframe_jcl", "mainframe_config": {"connection_id": "550e8400-e29b-41d4-a716-************", "job_class": "C", "job_type": "JCL", "data_transfers": [{"transfer_type": "download", "protocol": "SFTP", "source_path": "WORK.CUSTOMER.VALID.D${YYYYMMDD}", "destination_path": "/data/warehouse/customer/customer_${YYYYMMDD}.dat", "transfer_mode": "binary", "convert_encoding": true, "source_encoding": "EBCDIC", "target_encoding": "UTF-8", "retry_policy": {"max_attempts": 3, "delay_seconds": 60, "backoff_type": "fixed"}}]}, "condition": "{{ jobs.validate_customer_data.status == 'success' }}", "timeout": 900}, "process_db2_updates": {"id": "process_db2_updates", "name": "Process DB2 Updates", "type": "mainframe_db2", "mainframe_config": {"connection_id": "550e8400-e29b-41d4-a716-************", "job_class": "A", "job_type": "DB2", "jcl_template": "//DB2UPD   JOB CLASS=${JOB_CLASS},MSGCLASS=X,NOTIFY=&SYSUID\n//STEP1    EXEC PGM=IKJEFT01,DYNAMNBR=20\n//SYSTSPRT DD SYSOUT=*\n//SYSTSIN  DD *\nDSN SYSTEM(${DB2_SYSTEM})\nRUN PROGRAM(DSNTEP2) PLAN(${PLAN_NAME}) -\n    PARMS('${SQL_PARMS}')\nEND\n/*\n//SYSIN    DD DSN=${SQL_SCRIPT},DISP=SHR\n//SYSPRINT DD SYSOUT=*\n//SYSUDUMP DD SYSOUT=*", "jcl_parameters": {"JOB_CLASS": "A", "DB2_SYSTEM": "DB2P", "PLAN_NAME": "CUSTUPD", "SQL_PARMS": "SQL", "SQL_SCRIPT": "PROD.SQL.SCRIPTS(CUSTUPD)"}}, "condition": "{{ jobs.transfer_to_warehouse.status == 'success' }}", "timeout": 2400}, "generate_reports": {"id": "generate_reports", "name": "Generate Customer Reports", "type": "mainframe_jcl", "mainframe_config": {"connection_id": "550e8400-e29b-41d4-a716-************", "job_class": "B", "job_type": "JCL", "jcl_template": "//CUSTRPT  JOB CLASS=${JOB_CLASS},MSGCLASS=X,NOTIFY=&SYSUID\n//STEP1    EXEC PGM=CUSTRPT\n//CUSTDATA DD DSN=${CUSTOMER_DATA},DISP=SHR\n//REPORT   DD DSN=${REPORT_DATASET},DISP=(NEW,CATLG,DELETE),\n//            UNIT=SYSDA,SPACE=(CYL,(3,1)),\n//            DCB=(RECFM=FBA,LRECL=133,BLKSIZE=13300)\n//SYSIN    DD *\nREPORT TYPE=${REPORT_TYPE}\nDATE=${REPORT_DATE}\n/*", "jcl_parameters": {"JOB_CLASS": "B", "CUSTOMER_DATA": "WORK.CUSTOMER.VALID.D${YYYYMMDD}", "REPORT_DATASET": "PROD.REPORTS.CUSTOMER.D${YYYYMMDD}", "REPORT_TYPE": "SUMMARY", "REPORT_DATE": "{{ workflow.parameters.business_date }}"}, "data_transfers": [{"transfer_type": "download", "protocol": "FTP", "source_path": "PROD.REPORTS.CUSTOMER.D${YYYYMMDD}", "destination_path": "/reports/customer/customer_report_${YYYYMMDD}.txt", "transfer_mode": "ascii", "convert_encoding": true, "source_encoding": "EBCDIC", "target_encoding": "UTF-8"}]}, "condition": "{{ jobs.process_db2_updates.status == 'success' }}", "timeout": 600}, "cleanup_work_datasets": {"id": "cleanup_work_datasets", "name": "Cleanup Work Datasets", "type": "mainframe_jcl", "mainframe_config": {"connection_id": "550e8400-e29b-41d4-a716-************", "job_class": "C", "job_type": "JCL", "dataset_operations": [{"operation": "delete", "dataset_name": "WORK.CUSTOMER.EXTRACT.D${YYYYMMDD}", "condition": "{{ jobs.validate_customer_data.status == 'success' }}"}, {"operation": "delete", "dataset_name": "WORK.CUSTOMER.VALID.D${YYYYMMDD}", "condition": "{{ jobs.transfer_to_warehouse.status == 'success' }}"}, {"operation": "delete", "dataset_name": "WORK.CUSTOMER.ERRORS.D${YYYYMMDD}", "on_error": "continue"}]}, "condition": "{{ jobs.generate_reports.status == 'completion' }}", "timeout": 300}, "send_completion_notification": {"id": "send_completion_notification", "name": "Send Completion Notification", "type": "mainframe_mq", "mainframe_config": {"connection_id": "550e8400-e29b-41d4-a716-************", "job_type": "MQ", "notification_rules": [{"trigger": "on_completion", "notification_type": "mq_message", "recipients": ["BATCH.COMPLETION.QUEUE"], "parameters": {"workflow_id": "{{ workflow.id }}", "business_date": "{{ workflow.parameters.business_date }}", "status": "{{ workflow.status }}", "records_processed": "{{ jobs.validate_customer_data.output.record_count }}", "execution_time": "{{ workflow.execution_time }}"}}, {"trigger": "on_completion", "notification_type": "email", "recipients": ["<EMAIL>"], "template": "batch_completion", "parameters": {"workflow_name": "{{ workflow.name }}", "business_date": "{{ workflow.parameters.business_date }}", "status": "{{ workflow.status }}"}}]}, "timeout": 120}}, "edges": [{"from": "extract_customer_data", "to": "validate_customer_data", "type": "success"}, {"from": "validate_customer_data", "to": "transfer_to_warehouse", "type": "success"}, {"from": "transfer_to_warehouse", "to": "process_db2_updates", "type": "success"}, {"from": "process_db2_updates", "to": "generate_reports", "type": "success"}, {"from": "generate_reports", "to": "cleanup_work_datasets", "type": "completion"}, {"from": "cleanup_work_datasets", "to": "send_completion_notification", "type": "completion"}, {"from": "validate_customer_data", "to": "send_completion_notification", "type": "failure", "condition": "{{ jobs.validate_customer_data.output.validation_passed == false }}"}], "parameters": {"business_date": {"type": "string", "description": "Business date to process (YYYY-MM-DD)", "default": "{{ previous_business_day }}", "validation": "date_format:YYYY-MM-DD"}, "environment": {"type": "string", "description": "Target environment", "default": "production", "allowed_values": ["development", "test", "production"]}, "force_rerun": {"type": "boolean", "description": "Force rerun even if already processed", "default": false}}, "variables": {"YYYYMMDD": "{{ workflow.parameters.business_date | date_format('YYYYMMDD') }}", "work_prefix": "WORK.BATCH.{{ workflow.parameters.business_date | date_format('YYYYMMDD') }}", "notification_level": "INFO"}, "schedule": {"cron": "0 1 * * 1-5", "timezone": "America/New_York", "enabled": true, "calendar": "business_days_us"}, "sla": {"max_duration_minutes": 180, "critical_path": ["extract_customer_data", "validate_customer_data", "transfer_to_warehouse", "process_db2_updates"], "notifications": [{"type": "email", "recipients": ["<EMAIL>"], "trigger": "sla_warning", "threshold": "80%"}, {"type": "sms", "recipients": ["+1234567890"], "trigger": "sla_breach"}]}, "error_handling": {"on_failure": {"escalation": [{"delay_minutes": 10, "notify": ["<EMAIL>"]}, {"delay_minutes": 30, "notify": ["<EMAIL>"]}], "auto_retry": {"enabled": true, "max_attempts": 1, "delay_minutes": 15}, "cleanup": {"delete_work_datasets": true, "send_failure_notification": true}}}, "security": {"required_permissions": ["mainframe_batch_execute", "customer_data_read"], "audit_level": "full", "data_classification": "confidential"}, "monitoring": {"enable_real_time": true, "metric_collection": ["job_status", "resource_usage", "data_quality"], "alert_thresholds": {"job_duration_minutes": 60, "memory_usage_percent": 80, "error_rate_percent": 5}}}