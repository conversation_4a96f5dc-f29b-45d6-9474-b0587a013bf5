{"name": "End-of-Day Financial Processing", "description": "Process daily financial transactions and generate reports", "version": "1.0", "jobs": {"validate_trading_day": {"id": "validate_trading_day", "name": "Validate Trading Day", "type": "job", "job_definition_id": "660e8400-e29b-41d4-a716-446655440001", "parameters": {"date": "{{ workflow.parameters.business_date }}", "market": "{{ workflow.parameters.market }}"}, "timeout": 300}, "reconcile_trades": {"id": "reconcile_trades", "name": "Reconcile Trade Data", "type": "job", "job_definition_id": "660e8400-e29b-41d4-a716-446655440002", "parameters": {"business_date": "{{ workflow.parameters.business_date }}", "sources": ["bloomberg", "reuters", "internal_systems"], "tolerance": 0.01}, "timeout": 1800, "retry_policy": {"max_attempts": 2, "delay_seconds": 300, "backoff_type": "fixed"}}, "calculate_positions": {"id": "calculate_positions", "name": "Calculate Portfolio Positions", "type": "job", "job_definition_id": "660e8400-e29b-41d4-a716-446655440003", "parameters": {"business_date": "{{ workflow.parameters.business_date }}", "portfolios": "{{ workflow.parameters.portfolios }}", "include_derivatives": true}, "timeout": 2400}, "risk_calculations": {"id": "risk_calculations", "name": "Risk Calculations", "type": "parallel", "jobs": [{"id": "var_calculation", "name": "Value at Risk Calculation", "type": "job", "job_definition_id": "660e8400-e29b-41d4-a716-446655440004", "parameters": {"confidence_level": 0.95, "holding_period": 1, "method": "historical_simulation"}}, {"id": "stress_testing", "name": "Stress Testing", "type": "job", "job_definition_id": "660e8400-e29b-41d4-a716-446655440005", "parameters": {"scenarios": ["market_crash", "interest_rate_shock", "credit_crisis"], "severity": "severe"}}, {"id": "exposure_limits", "name": "Check Exposure Limits", "type": "job", "job_definition_id": "660e8400-e29b-41d4-a716-446655440006", "parameters": {"limit_types": ["sector", "country", "currency", "single_name"]}}], "timeout": 3600}, "generate_reports": {"id": "generate_reports", "name": "Generate Financial Reports", "type": "parallel", "jobs": [{"id": "pnl_report", "name": "P&L Report", "type": "job", "job_definition_id": "660e8400-e29b-41d4-a716-446655440007", "parameters": {"report_type": "daily_pnl", "format": "pdf", "include_attribution": true}}, {"id": "risk_report", "name": "Risk Report", "type": "job", "job_definition_id": "660e8400-e29b-41d4-a716-446655440008", "parameters": {"report_type": "daily_risk", "format": "excel", "include_scenarios": true}}, {"id": "regulatory_report", "name": "Regulatory Report", "type": "job", "job_definition_id": "660e8400-e29b-41d4-a716-446655440009", "parameters": {"regulations": ["basel_iii", "mifid_ii", "dodd_frank"], "format": "xml"}}], "timeout": 1800}, "data_quality_checks": {"id": "data_quality_checks", "name": "Data Quality Validation", "type": "job", "job_definition_id": "660e8400-e29b-41d4-a716-446655440010", "parameters": {"checks": ["completeness", "accuracy", "consistency", "timeliness"], "threshold": 0.95}, "timeout": 900}, "archive_data": {"id": "archive_data", "name": "Archive Daily Data", "type": "job", "job_definition_id": "660e8400-e29b-41d4-a716-446655440011", "parameters": {"business_date": "{{ workflow.parameters.business_date }}", "retention_policy": "7_years", "compression": true}, "timeout": 1200}, "send_notifications": {"id": "send_notifications", "name": "Send Completion Notifications", "type": "condition", "conditions": [{"condition": "{{ jobs.data_quality_checks.output.quality_score >= 0.95 }}", "action": {"type": "job", "job_definition_id": "660e8400-e29b-41d4-a716-446655440012", "parameters": {"recipients": ["<EMAIL>", "<EMAIL>"], "subject": "EOD Processing Completed Successfully", "template": "eod_success"}}}, {"condition": "{{ jobs.data_quality_checks.output.quality_score < 0.95 }}", "action": {"type": "job", "job_definition_id": "660e8400-e29b-41d4-a716-446655440013", "parameters": {"recipients": ["<EMAIL>", "<EMAIL>", "<EMAIL>"], "subject": "EOD Processing - Data Quality Issues Detected", "template": "eod_quality_warning", "priority": "high"}}}]}}, "edges": [{"from": "validate_trading_day", "to": "reconcile_trades", "type": "success"}, {"from": "reconcile_trades", "to": "calculate_positions", "type": "success"}, {"from": "calculate_positions", "to": "risk_calculations", "type": "success"}, {"from": "risk_calculations", "to": "generate_reports", "type": "success"}, {"from": "generate_reports", "to": "data_quality_checks", "type": "success"}, {"from": "data_quality_checks", "to": "archive_data", "type": "success"}, {"from": "data_quality_checks", "to": "send_notifications", "type": "completion"}], "parameters": {"business_date": {"type": "string", "description": "Business date to process (YYYY-MM-DD)", "default": "{{ previous_business_day }}"}, "market": {"type": "string", "description": "Market to process", "default": "US", "allowed_values": ["US", "EU", "ASIA"]}, "portfolios": {"type": "array", "description": "List of portfolios to process", "default": ["all"]}, "emergency_mode": {"type": "boolean", "description": "Run in emergency mode (skip non-critical checks)", "default": false}}, "variables": {"cutoff_time": "18:00:00", "timezone": "America/New_York", "max_retry_attempts": 3}, "schedule": {"cron": "0 19 * * 1-5", "timezone": "America/New_York", "enabled": true, "calendar": "business_days_us"}, "sla": {"max_duration_minutes": 240, "critical_path": ["validate_trading_day", "reconcile_trades", "calculate_positions", "risk_calculations", "generate_reports"], "notifications": [{"type": "email", "recipients": ["<EMAIL>"], "trigger": "sla_warning", "threshold": "80%"}, {"type": "sms", "recipients": ["+1234567890"], "trigger": "sla_breach"}]}, "error_handling": {"on_failure": {"escalation": [{"delay_minutes": 5, "notify": ["<EMAIL>"]}, {"delay_minutes": 15, "notify": ["<EMAIL>"]}, {"delay_minutes": 30, "notify": ["<EMAIL>"]}], "auto_retry": {"enabled": true, "max_attempts": 2, "delay_minutes": 10}}}, "compliance": {"audit_trail": true, "data_retention": "7_years", "encryption": "aes_256", "access_control": "rbac"}}