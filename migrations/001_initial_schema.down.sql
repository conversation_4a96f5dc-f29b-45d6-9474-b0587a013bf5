-- WorkflowMaster Initial Schema Rollback
-- This migration drops all tables and extensions created in the initial schema

-- Drop indexes first
DROP INDEX IF EXISTS idx_schedules_active_job;
DROP INDEX IF EXISTS idx_job_instances_def_status;
DROP INDEX IF EXISTS idx_job_instances_status_scheduled;

DROP INDEX IF EXISTS idx_notifications_type;
DROP INDEX IF EXISTS idx_notifications_status;
DROP INDEX IF EXISTS idx_notifications_user;

DROP INDEX IF EXISTS idx_audit_logs_created_at;
DROP INDEX IF EXISTS idx_audit_logs_resource;
DROP INDEX IF EXISTS idx_audit_logs_action;
DROP INDEX IF EXISTS idx_audit_logs_user;

DROP INDEX IF EXISTS idx_events_created_at;
DROP INDEX IF EXISTS idx_events_processed;
DROP INDEX IF EXISTS idx_events_type;

DROP INDEX IF EXISTS idx_execution_nodes_heartbeat;
DROP INDEX IF EXISTS idx_execution_nodes_resource_pool;
DROP INDEX IF EXISTS idx_execution_nodes_status;

DROP INDEX IF EXISTS idx_workflow_instances_workflow;
DROP INDEX IF EXISTS idx_workflow_instances_scheduled_at;
DROP INDEX IF EXISTS idx_workflow_instances_status;

DROP INDEX IF EXISTS idx_workflows_owner;
DROP INDEX IF EXISTS idx_workflows_active;
DROP INDEX IF EXISTS idx_workflows_name;

DROP INDEX IF EXISTS idx_schedules_end_date;
DROP INDEX IF EXISTS idx_schedules_start_date;
DROP INDEX IF EXISTS idx_schedules_job_def;
DROP INDEX IF EXISTS idx_schedules_active;

DROP INDEX IF EXISTS idx_job_instances_priority;
DROP INDEX IF EXISTS idx_job_instances_node;
DROP INDEX IF EXISTS idx_job_instances_workflow;
DROP INDEX IF EXISTS idx_job_instances_job_def;
DROP INDEX IF EXISTS idx_job_instances_scheduled_at;
DROP INDEX IF EXISTS idx_job_instances_status;

DROP INDEX IF EXISTS idx_job_definitions_resource_pool;
DROP INDEX IF EXISTS idx_job_definitions_active;
DROP INDEX IF EXISTS idx_job_definitions_owner;
DROP INDEX IF EXISTS idx_job_definitions_type;
DROP INDEX IF EXISTS idx_job_definitions_name;

DROP INDEX IF EXISTS idx_users_active;
DROP INDEX IF EXISTS idx_users_email;
DROP INDEX IF EXISTS idx_users_username;

-- Drop tables in reverse dependency order
DROP TABLE IF EXISTS notifications;
DROP TABLE IF EXISTS audit_logs;
DROP TABLE IF EXISTS sla_definitions;
DROP TABLE IF EXISTS calendars;
DROP TABLE IF EXISTS event_triggers;
DROP TABLE IF EXISTS events;
DROP TABLE IF EXISTS job_dependencies;
DROP TABLE IF EXISTS workflow_instances;
DROP TABLE IF EXISTS job_instances;
DROP TABLE IF EXISTS schedules;
DROP TABLE IF EXISTS workflows;
DROP TABLE IF EXISTS job_definitions;
DROP TABLE IF EXISTS execution_nodes;
DROP TABLE IF EXISTS resource_pools;
DROP TABLE IF EXISTS user_roles;
DROP TABLE IF EXISTS roles;
DROP TABLE IF EXISTS users;

-- Drop extensions
DROP EXTENSION IF EXISTS "uuid-ossp";
