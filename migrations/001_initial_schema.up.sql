-- WorkflowMaster Initial Database Schema
-- This migration creates the core tables for the job scheduling and workflow orchestration system

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users and Authentication
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VA<PERSON>HA<PERSON>(255) UNIQUE NOT NULL,
    email VA<PERSON>HAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VA<PERSON><PERSON><PERSON>(255),
    last_name VA<PERSON><PERSON><PERSON>(255),
    is_active BOOLEAN DEFAULT true,
    is_admin BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login_at TIMESTAMP WITH TIME ZONE
);

-- Roles and Permissions
CREATE TABLE roles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    permissions JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE user_roles (
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    role_id UUID REFERENCES roles(id) ON DELETE CASCADE,
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    assigned_by UUID REFERENCES users(id),
    PRIMARY KEY (user_id, role_id)
);

-- Resource Pools
CREATE TABLE resource_pools (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    max_concurrent_jobs INTEGER DEFAULT 10,
    priority INTEGER DEFAULT 100,
    tags JSONB DEFAULT '[]',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Execution Nodes
CREATE TABLE execution_nodes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) UNIQUE NOT NULL,
    hostname VARCHAR(255) NOT NULL,
    ip_address INET,
    port INTEGER DEFAULT 8080,
    platform VARCHAR(50) NOT NULL, -- linux, windows, darwin
    architecture VARCHAR(50) NOT NULL, -- amd64, arm64
    resource_pool_id UUID REFERENCES resource_pools(id),
    max_concurrent_jobs INTEGER DEFAULT 5,
    current_jobs INTEGER DEFAULT 0,
    cpu_cores INTEGER,
    memory_mb INTEGER,
    disk_space_gb INTEGER,
    status VARCHAR(50) DEFAULT 'offline', -- online, offline, maintenance, error
    last_heartbeat TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Job Definitions
CREATE TABLE job_definitions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    job_type VARCHAR(100) NOT NULL, -- shell, sql, http, python, docker, etc.
    command TEXT NOT NULL,
    working_directory VARCHAR(500),
    environment_variables JSONB DEFAULT '{}',
    parameters JSONB DEFAULT '{}',
    timeout_seconds INTEGER DEFAULT 3600,
    retry_count INTEGER DEFAULT 0,
    retry_delay_seconds INTEGER DEFAULT 60,
    resource_pool_id UUID REFERENCES resource_pools(id),
    owner_id UUID REFERENCES users(id),
    is_active BOOLEAN DEFAULT true,
    tags JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Schedules
CREATE TABLE schedules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    job_definition_id UUID REFERENCES job_definitions(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    cron_expression VARCHAR(255),
    timezone VARCHAR(100) DEFAULT 'UTC',
    start_date TIMESTAMP WITH TIME ZONE,
    end_date TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    max_instances INTEGER DEFAULT 1,
    overlap_policy VARCHAR(50) DEFAULT 'skip', -- skip, queue, replace
    calendar_id UUID, -- Reference to calendar for business day scheduling
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Workflows
CREATE TABLE workflows (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    definition JSONB NOT NULL, -- Workflow DAG definition
    version INTEGER DEFAULT 1,
    is_active BOOLEAN DEFAULT true,
    owner_id UUID REFERENCES users(id),
    tags JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Job Instances (Executions)
CREATE TABLE job_instances (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    job_definition_id UUID REFERENCES job_definitions(id),
    workflow_instance_id UUID, -- References workflow_instances(id)
    schedule_id UUID REFERENCES schedules(id),
    execution_node_id UUID REFERENCES execution_nodes(id),
    status VARCHAR(50) DEFAULT 'pending', -- pending, running, success, failed, cancelled, timeout
    priority INTEGER DEFAULT 100,
    scheduled_at TIMESTAMP WITH TIME ZONE NOT NULL,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    exit_code INTEGER,
    output_log TEXT,
    error_log TEXT,
    parameters JSONB DEFAULT '{}',
    environment_variables JSONB DEFAULT '{}',
    resource_usage JSONB DEFAULT '{}', -- CPU, memory, disk usage stats
    retry_attempt INTEGER DEFAULT 0,
    parent_instance_id UUID REFERENCES job_instances(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Workflow Instances
CREATE TABLE workflow_instances (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    workflow_id UUID REFERENCES workflows(id),
    status VARCHAR(50) DEFAULT 'pending', -- pending, running, success, failed, cancelled
    scheduled_at TIMESTAMP WITH TIME ZONE NOT NULL,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    parameters JSONB DEFAULT '{}',
    execution_context JSONB DEFAULT '{}',
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Job Dependencies
CREATE TABLE job_dependencies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    parent_job_id UUID REFERENCES job_definitions(id) ON DELETE CASCADE,
    child_job_id UUID REFERENCES job_definitions(id) ON DELETE CASCADE,
    dependency_type VARCHAR(50) DEFAULT 'success', -- success, failure, completion, always
    condition_expression TEXT, -- Optional condition for complex dependencies
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(parent_job_id, child_job_id)
);

-- Events and Triggers
CREATE TABLE events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_type VARCHAR(100) NOT NULL, -- file_arrival, database_change, api_call, schedule, manual
    source VARCHAR(255) NOT NULL,
    payload JSONB DEFAULT '{}',
    processed BOOLEAN DEFAULT false,
    processed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE event_triggers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    job_definition_id UUID REFERENCES job_definitions(id) ON DELETE CASCADE,
    event_type VARCHAR(100) NOT NULL,
    condition_expression TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Calendars for Business Day Scheduling
CREATE TABLE calendars (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    timezone VARCHAR(100) DEFAULT 'UTC',
    business_days INTEGER[] DEFAULT '{1,2,3,4,5}', -- 1=Monday, 7=Sunday
    holidays JSONB DEFAULT '[]', -- Array of holiday dates
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- SLA Definitions
CREATE TABLE sla_definitions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    job_definition_id UUID REFERENCES job_definitions(id),
    workflow_id UUID REFERENCES workflows(id),
    max_duration_seconds INTEGER,
    max_retry_count INTEGER,
    success_rate_threshold DECIMAL(5,2), -- Percentage
    notification_rules JSONB DEFAULT '[]',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Audit Log
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id),
    action VARCHAR(255) NOT NULL,
    resource_type VARCHAR(100) NOT NULL,
    resource_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Notifications
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id),
    type VARCHAR(100) NOT NULL, -- email, slack, webhook, sms
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    status VARCHAR(50) DEFAULT 'pending', -- pending, sent, failed
    sent_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add foreign key for workflow_instances in job_instances
ALTER TABLE job_instances ADD CONSTRAINT fk_workflow_instance
    FOREIGN KEY (workflow_instance_id) REFERENCES workflow_instances(id);

-- Add foreign key for calendar in schedules
ALTER TABLE schedules ADD CONSTRAINT fk_calendar
    FOREIGN KEY (calendar_id) REFERENCES calendars(id);

-- Create Indexes for Performance
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_active ON users(is_active);

CREATE INDEX idx_job_definitions_name ON job_definitions(name);
CREATE INDEX idx_job_definitions_type ON job_definitions(job_type);
CREATE INDEX idx_job_definitions_owner ON job_definitions(owner_id);
CREATE INDEX idx_job_definitions_active ON job_definitions(is_active);
CREATE INDEX idx_job_definitions_resource_pool ON job_definitions(resource_pool_id);

CREATE INDEX idx_job_instances_status ON job_instances(status);
CREATE INDEX idx_job_instances_scheduled_at ON job_instances(scheduled_at);
CREATE INDEX idx_job_instances_job_def ON job_instances(job_definition_id);
CREATE INDEX idx_job_instances_workflow ON job_instances(workflow_instance_id);
CREATE INDEX idx_job_instances_node ON job_instances(execution_node_id);
CREATE INDEX idx_job_instances_priority ON job_instances(priority);

CREATE INDEX idx_schedules_active ON schedules(is_active);
CREATE INDEX idx_schedules_job_def ON schedules(job_definition_id);
CREATE INDEX idx_schedules_start_date ON schedules(start_date);
CREATE INDEX idx_schedules_end_date ON schedules(end_date);

CREATE INDEX idx_workflows_name ON workflows(name);
CREATE INDEX idx_workflows_active ON workflows(is_active);
CREATE INDEX idx_workflows_owner ON workflows(owner_id);

CREATE INDEX idx_workflow_instances_status ON workflow_instances(status);
CREATE INDEX idx_workflow_instances_scheduled_at ON workflow_instances(scheduled_at);
CREATE INDEX idx_workflow_instances_workflow ON workflow_instances(workflow_id);

CREATE INDEX idx_execution_nodes_status ON execution_nodes(status);
CREATE INDEX idx_execution_nodes_resource_pool ON execution_nodes(resource_pool_id);
CREATE INDEX idx_execution_nodes_heartbeat ON execution_nodes(last_heartbeat);

CREATE INDEX idx_events_type ON events(event_type);
CREATE INDEX idx_events_processed ON events(processed);
CREATE INDEX idx_events_created_at ON events(created_at);

CREATE INDEX idx_audit_logs_user ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_action ON audit_logs(action);
CREATE INDEX idx_audit_logs_resource ON audit_logs(resource_type, resource_id);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);

CREATE INDEX idx_notifications_user ON notifications(user_id);
CREATE INDEX idx_notifications_status ON notifications(status);
CREATE INDEX idx_notifications_type ON notifications(type);

-- Create composite indexes for common queries
CREATE INDEX idx_job_instances_status_scheduled ON job_instances(status, scheduled_at);
CREATE INDEX idx_job_instances_def_status ON job_instances(job_definition_id, status);
CREATE INDEX idx_schedules_active_job ON schedules(is_active, job_definition_id);
