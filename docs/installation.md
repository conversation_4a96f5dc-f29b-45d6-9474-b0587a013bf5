# WorkflowMaster Installation Guide

This guide will help you install and configure WorkflowMaster in various environments.

## Prerequisites

### System Requirements
- **Operating System**: Linux (Ubuntu 20.04+, CentOS 8+, RHEL 8+), macOS 10.15+, Windows 10+
- **CPU**: 2+ cores recommended
- **Memory**: 4GB RAM minimum, 8GB+ recommended
- **Storage**: 20GB+ available space
- **Network**: Internet access for downloading dependencies

### Software Dependencies
- **Go**: 1.21 or later
- **Node.js**: 18.0 or later
- **PostgreSQL**: 15.0 or later
- **Redis**: 7.0 or later
- **Docker**: 20.10+ (optional, for containerized deployment)
- **Kubernetes**: 1.25+ (optional, for Kubernetes deployment)

## Installation Methods

### 1. Binary Installation (Recommended for Production)

#### Download Pre-built Binaries
```bash
# Download the latest release
wget https://github.com/workflowmaster/workflowmaster/releases/latest/download/workflowmaster-linux-amd64.tar.gz

# Extract the archive
tar -xzf workflowmaster-linux-amd64.tar.gz

# Move binaries to system path
sudo mv workflowmaster-*/bin/* /usr/local/bin/

# Verify installation
workflowmaster-api-gateway --version
```

#### Create System User
```bash
# Create workflowmaster user
sudo useradd -r -s /bin/false workflowmaster

# Create directories
sudo mkdir -p /etc/workflowmaster
sudo mkdir -p /var/lib/workflowmaster
sudo mkdir -p /var/log/workflowmaster

# Set permissions
sudo chown -R workflowmaster:workflowmaster /var/lib/workflowmaster
sudo chown -R workflowmaster:workflowmaster /var/log/workflowmaster
```

### 2. Docker Installation

#### Using Docker Compose (Recommended for Development)
```bash
# Clone the repository
git clone https://github.com/workflowmaster/workflowmaster.git
cd workflowmaster

# Start all services
docker-compose up -d

# Check service status
docker-compose ps
```

#### Using Individual Docker Images
```bash
# Pull images
docker pull workflowmaster/api-gateway:latest
docker pull workflowmaster/scheduler:latest
docker pull workflowmaster/executor:latest

# Run PostgreSQL
docker run -d --name workflowmaster-postgres \
  -e POSTGRES_DB=workflowmaster \
  -e POSTGRES_USER=workflowmaster \
  -e POSTGRES_PASSWORD=password \
  -p 5432:5432 \
  postgres:15-alpine

# Run Redis
docker run -d --name workflowmaster-redis \
  -p 6379:6379 \
  redis:7-alpine

# Run API Gateway
docker run -d --name workflowmaster-api \
  -p 8080:8080 \
  -e WM_DATABASE_HOST=host.docker.internal \
  -e WM_DATABASE_PASSWORD=password \
  workflowmaster/api-gateway:latest
```

### 3. Kubernetes Installation

#### Using Helm (Recommended)
```bash
# Add Helm repository
helm repo add workflowmaster https://charts.workflowmaster.io
helm repo update

# Install with default values
helm install workflowmaster workflowmaster/workflowmaster

# Install with custom values
helm install workflowmaster workflowmaster/workflowmaster \
  --set database.password=your-secure-password \
  --set auth.jwtSecret=your-jwt-secret
```

#### Using Kubernetes Manifests
```bash
# Apply manifests
kubectl apply -f deployments/k8s/

# Check deployment status
kubectl get pods -l app=workflowmaster
```

### 4. Source Installation (Development)

#### Prerequisites
```bash
# Install Go
wget https://go.dev/dl/go1.21.0.linux-amd64.tar.gz
sudo tar -C /usr/local -xzf go1.21.0.linux-amd64.tar.gz
export PATH=$PATH:/usr/local/go/bin

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install development tools
go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
go install golang.org/x/tools/cmd/goimports@latest
```

#### Build from Source
```bash
# Clone repository
git clone https://github.com/workflowmaster/workflowmaster.git
cd workflowmaster

# Install dependencies
make deps

# Build all services
make build

# Run tests
make test

# Start development environment
make dev-infra
make dev-backend
```

## Database Setup

### PostgreSQL Configuration
```sql
-- Create database and user
CREATE DATABASE workflowmaster;
CREATE USER workflowmaster WITH PASSWORD 'your-secure-password';
GRANT ALL PRIVILEGES ON DATABASE workflowmaster TO workflowmaster;

-- Enable required extensions
\c workflowmaster
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
```

### Run Database Migrations
```bash
# Using the binary
workflowmaster-api-gateway migrate up

# Using make (development)
make migrate-up

# Using Docker
docker run --rm \
  -e WM_DATABASE_HOST=your-db-host \
  -e WM_DATABASE_PASSWORD=your-password \
  workflowmaster/api-gateway:latest migrate up
```

## Configuration

### Basic Configuration File
Create `/etc/workflowmaster/config.yaml`:
```yaml
server:
  host: "0.0.0.0"
  port: 8080

database:
  host: "localhost"
  port: 5432
  user: "workflowmaster"
  password: "your-secure-password"
  name: "workflowmaster"

auth:
  jwt_secret: "your-jwt-secret-change-this"

redis:
  host: "localhost"
  port: 6379
```

### Environment Variables
```bash
# Database configuration
export WM_DATABASE_HOST=localhost
export WM_DATABASE_PORT=5432
export WM_DATABASE_USER=workflowmaster
export WM_DATABASE_PASSWORD=your-secure-password
export WM_DATABASE_NAME=workflowmaster

# Authentication
export WM_AUTH_JWT_SECRET=your-jwt-secret

# Server configuration
export WM_SERVER_HOST=0.0.0.0
export WM_SERVER_PORT=8080
```

## Service Configuration

### Systemd Service Files

#### API Gateway Service
Create `/etc/systemd/system/workflowmaster-api.service`:
```ini
[Unit]
Description=WorkflowMaster API Gateway
After=network.target postgresql.service redis.service

[Service]
Type=simple
User=workflowmaster
Group=workflowmaster
ExecStart=/usr/local/bin/workflowmaster-api-gateway
Restart=always
RestartSec=5
Environment=WM_CONFIG_FILE=/etc/workflowmaster/config.yaml

[Install]
WantedBy=multi-user.target
```

#### Scheduler Service
Create `/etc/systemd/system/workflowmaster-scheduler.service`:
```ini
[Unit]
Description=WorkflowMaster Scheduler
After=network.target postgresql.service

[Service]
Type=simple
User=workflowmaster
Group=workflowmaster
ExecStart=/usr/local/bin/workflowmaster-scheduler
Restart=always
RestartSec=5
Environment=WM_CONFIG_FILE=/etc/workflowmaster/config.yaml

[Install]
WantedBy=multi-user.target
```

#### Executor Service
Create `/etc/systemd/system/workflowmaster-executor.service`:
```ini
[Unit]
Description=WorkflowMaster Executor
After=network.target postgresql.service

[Service]
Type=simple
User=workflowmaster
Group=workflowmaster
ExecStart=/usr/local/bin/workflowmaster-executor
Restart=always
RestartSec=5
Environment=WM_CONFIG_FILE=/etc/workflowmaster/config.yaml

[Install]
WantedBy=multi-user.target
```

### Enable and Start Services
```bash
# Reload systemd
sudo systemctl daemon-reload

# Enable services
sudo systemctl enable workflowmaster-api
sudo systemctl enable workflowmaster-scheduler
sudo systemctl enable workflowmaster-executor

# Start services
sudo systemctl start workflowmaster-api
sudo systemctl start workflowmaster-scheduler
sudo systemctl start workflowmaster-executor

# Check status
sudo systemctl status workflowmaster-api
sudo systemctl status workflowmaster-scheduler
sudo systemctl status workflowmaster-executor
```

## Initial Setup

### Create Admin User
```bash
# Using the CLI
workflowmaster-cli user create \
  --username admin \
  --email <EMAIL> \
  --password your-admin-password \
  --admin

# Using the API
curl -X POST http://localhost:8080/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "email": "<EMAIL>",
    "password": "your-admin-password"
  }'
```

### Verify Installation
```bash
# Check API health
curl http://localhost:8080/health

# Check service status
curl http://localhost:8080/api/v1/system/health

# Access web interface
open http://localhost:8080
```

## Security Considerations

### SSL/TLS Configuration
```yaml
server:
  tls:
    enabled: true
    cert_file: "/etc/workflowmaster/tls/server.crt"
    key_file: "/etc/workflowmaster/tls/server.key"
```

### Firewall Configuration
```bash
# Allow HTTP/HTTPS traffic
sudo ufw allow 8080/tcp
sudo ufw allow 8443/tcp

# Allow database access (if external)
sudo ufw allow from 10.0.0.0/8 to any port 5432
```

### Database Security
```sql
-- Create read-only user for monitoring
CREATE USER workflowmaster_readonly WITH PASSWORD 'readonly-password';
GRANT CONNECT ON DATABASE workflowmaster TO workflowmaster_readonly;
GRANT USAGE ON SCHEMA public TO workflowmaster_readonly;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO workflowmaster_readonly;
```

## Troubleshooting

### Common Issues

#### Database Connection Issues
```bash
# Check database connectivity
pg_isready -h localhost -p 5432 -U workflowmaster

# Check logs
sudo journalctl -u workflowmaster-api -f
```

#### Permission Issues
```bash
# Fix file permissions
sudo chown -R workflowmaster:workflowmaster /var/lib/workflowmaster
sudo chmod -R 755 /var/lib/workflowmaster
```

#### Port Conflicts
```bash
# Check port usage
sudo netstat -tlnp | grep :8080

# Kill conflicting processes
sudo fuser -k 8080/tcp
```

### Log Locations
- **System logs**: `/var/log/workflowmaster/`
- **Systemd logs**: `journalctl -u workflowmaster-*`
- **Application logs**: Check configuration for log output location

## Next Steps

1. **Configure Authentication**: Set up LDAP/SSO integration
2. **Set up Monitoring**: Configure Prometheus and Grafana
3. **Create Workflows**: Start building your first workflows
4. **Set up Backups**: Configure automated database backups
5. **Scale Deployment**: Add more executor nodes for high availability

For more detailed configuration options, see the [Configuration Guide](configuration.md).
For usage examples, see the [User Guide](user-guide.md).
