# WorkflowMaster Configuration File

# Server Configuration
server:
  host: "0.0.0.0"
  port: 8080
  debug: false
  read_timeout: "30s"
  write_timeout: "30s"
  cors_origins:
    - "*"

# Database Configuration
database:
  host: "localhost"
  port: 5432
  user: "workflowmaster"
  password: "password"
  name: "workflowmaster"
  sslmode: "disable"
  timezone: "UTC"
  auto_migrate: true
  seed_data: true

# Authentication Configuration
auth:
  jwt_secret: "your-secret-key-change-this-in-production"
  token_expiry: "24h"
  refresh_token_expiry: "168h" # 7 days

# Scheduler Configuration
scheduler:
  max_concurrent_jobs: 100
  job_timeout: "1h"
  heartbeat_interval: "30s"

# Executor Configuration
executor:
  max_concurrent_jobs: 5
  heartbeat_interval: "30s"
  job_timeout: "1h"

# Workflow Engine Configuration
workflow:
  max_concurrent_workflows: 50
  default_timeout: "2h"

# Redis Configuration (for caching and sessions)
redis:
  host: "localhost"
  port: 6379
  password: ""
  db: 0

# Message Queue Configuration
message_queue:
  type: "redis" # redis, rabbitmq
  url: "redis://localhost:6379"

# Monitoring Configuration
monitoring:
  enabled: true
  metrics_port: 9090
  health_check_interval: "10s"

# Logging Configuration
logging:
  level: "info" # debug, info, warn, error
  format: "json" # json, text
  output: "stdout" # stdout, file
  file_path: "/var/log/workflowmaster/app.log"

# Security Configuration
security:
  rate_limiting:
    enabled: true
    requests_per_minute: 100
  cors:
    enabled: true
    allowed_origins: ["*"]
    allowed_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    allowed_headers: ["Origin", "Content-Type", "Authorization"]

# Notification Configuration
notifications:
  email:
    enabled: false
    smtp_host: "smtp.gmail.com"
    smtp_port: 587
    username: ""
    password: ""
    from_address: "<EMAIL>"
  
  slack:
    enabled: false
    webhook_url: ""
    channel: "#workflowmaster"
  
  webhook:
    enabled: false
    url: ""
    timeout: "10s"

# File Storage Configuration
storage:
  type: "local" # local, s3, gcs
  local:
    path: "/var/lib/workflowmaster/storage"
  s3:
    bucket: ""
    region: ""
    access_key: ""
    secret_key: ""

# Plugin Configuration
plugins:
  enabled: true
  directory: "/etc/workflowmaster/plugins"
  auto_load: true

# Backup Configuration
backup:
  enabled: false
  schedule: "0 2 * * *" # Daily at 2 AM
  retention_days: 30
  storage_type: "local" # local, s3
  path: "/var/backups/workflowmaster"

# High Availability Configuration
ha:
  enabled: false
  cluster_name: "workflowmaster-cluster"
  node_id: ""
  discovery:
    type: "static" # static, consul, etcd
    nodes:
      - "node1:8080"
      - "node2:8080"
      - "node3:8080"

# Performance Tuning
performance:
  database:
    max_open_connections: 100
    max_idle_connections: 10
    connection_max_lifetime: "1h"
  
  cache:
    enabled: true
    ttl: "5m"
    max_size: "100MB"
  
  job_queue:
    buffer_size: 10000
    worker_count: 10

# Development Configuration
development:
  hot_reload: false
  debug_endpoints: false
  mock_external_services: false
