import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline, Box } from '@mui/material';
import { QueryClient, QueryClientProvider } from 'react-query';
import { Provider } from 'react-redux';
import { SnackbarProvider } from 'notistack';
import { HelmetProvider } from 'react-helmet-async';

import { store } from './store';
import { AuthProvider } from './contexts/AuthContext';
import { SocketProvider } from './contexts/SocketContext';
import Layout from './components/Layout/Layout';
import ProtectedRoute from './components/Auth/ProtectedRoute';

// Pages
import Login from './pages/Auth/Login';
import Dashboard from './pages/Dashboard/Dashboard';
import Jobs from './pages/Jobs/Jobs';
import JobDetail from './pages/Jobs/JobDetail';
import Workflows from './pages/Workflows/Workflows';
import WorkflowDetail from './pages/Workflows/WorkflowDetail';
import WorkflowDesigner from './pages/Workflows/WorkflowDesigner';
import Schedules from './pages/Schedules/Schedules';
import Monitoring from './pages/Monitoring/Monitoring';
import Resources from './pages/Resources/Resources';
import Users from './pages/Admin/Users';
import Settings from './pages/Settings/Settings';
import NotFound from './pages/NotFound/NotFound';

// Create theme
const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
    background: {
      default: '#f5f5f5',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontSize: '2.5rem',
      fontWeight: 500,
    },
    h2: {
      fontSize: '2rem',
      fontWeight: 500,
    },
    h3: {
      fontSize: '1.75rem',
      fontWeight: 500,
    },
  },
  components: {
    MuiAppBar: {
      styleOverrides: {
        root: {
          backgroundColor: '#1976d2',
        },
      },
    },
    MuiDrawer: {
      styleOverrides: {
        paper: {
          backgroundColor: '#fafafa',
        },
      },
    },
  },
});

// Create React Query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

function App() {
  return (
    <HelmetProvider>
      <Provider store={store}>
        <QueryClientProvider client={queryClient}>
          <ThemeProvider theme={theme}>
            <CssBaseline />
            <SnackbarProvider 
              maxSnack={3}
              anchorOrigin={{
                vertical: 'top',
                horizontal: 'right',
              }}
            >
              <AuthProvider>
                <SocketProvider>
                  <Router>
                    <Box sx={{ display: 'flex', minHeight: '100vh' }}>
                      <Routes>
                        {/* Public routes */}
                        <Route path="/login" element={<Login />} />
                        
                        {/* Protected routes */}
                        <Route path="/" element={
                          <ProtectedRoute>
                            <Layout />
                          </ProtectedRoute>
                        }>
                          <Route index element={<Navigate to="/dashboard" replace />} />
                          <Route path="dashboard" element={<Dashboard />} />
                          
                          {/* Jobs */}
                          <Route path="jobs" element={<Jobs />} />
                          <Route path="jobs/:id" element={<JobDetail />} />
                          
                          {/* Workflows */}
                          <Route path="workflows" element={<Workflows />} />
                          <Route path="workflows/:id" element={<WorkflowDetail />} />
                          <Route path="workflows/:id/designer" element={<WorkflowDesigner />} />
                          <Route path="workflows/new" element={<WorkflowDesigner />} />
                          
                          {/* Schedules */}
                          <Route path="schedules" element={<Schedules />} />
                          
                          {/* Monitoring */}
                          <Route path="monitoring" element={<Monitoring />} />
                          
                          {/* Resources */}
                          <Route path="resources" element={<Resources />} />
                          
                          {/* Admin */}
                          <Route path="admin/users" element={<Users />} />
                          
                          {/* Settings */}
                          <Route path="settings" element={<Settings />} />
                        </Route>
                        
                        {/* 404 */}
                        <Route path="*" element={<NotFound />} />
                      </Routes>
                    </Box>
                  </Router>
                </SocketProvider>
              </AuthProvider>
            </SnackbarProvider>
          </ThemeProvider>
        </QueryClientProvider>
      </Provider>
    </HelmetProvider>
  );
}

export default App;
