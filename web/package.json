{"name": "workflowmaster-web", "version": "1.0.0", "description": "WorkflowMaster Web Dashboard", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "@reduxjs/toolkit": "^1.9.1", "react-redux": "^8.0.5", "@mui/material": "^5.11.0", "@mui/icons-material": "^5.11.0", "@mui/x-data-grid": "^5.17.0", "@mui/x-date-pickers": "^5.0.0", "@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "axios": "^1.2.0", "date-fns": "^2.29.0", "recharts": "^2.4.0", "react-flow-renderer": "^10.3.0", "react-query": "^3.39.0", "socket.io-client": "^4.6.0", "react-helmet-async": "^1.3.0", "react-hook-form": "^7.42.0", "yup": "^0.32.0", "@hookform/resolvers": "^2.9.0", "notistack": "^2.0.0", "react-beautiful-dnd": "^13.1.0", "monaco-editor": "^0.34.0", "@monaco-editor/react": "^4.4.0", "js-yaml": "^4.1.0", "lodash": "^4.17.0", "dayjs": "^1.11.0"}, "devDependencies": {"@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/node": "^18.0.0", "@types/lodash": "^4.14.0", "@types/js-yaml": "^4.0.0", "@typescript-eslint/eslint-plugin": "^5.48.0", "@typescript-eslint/parser": "^5.48.0", "@vitejs/plugin-react": "^3.0.0", "eslint": "^8.31.0", "eslint-plugin-react": "^7.31.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.0", "typescript": "^4.9.0", "vite": "^4.0.0", "vitest": "^0.26.0", "@vitest/ui": "^0.26.0", "c8": "^7.12.0", "jsdom": "^20.0.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^5.16.0", "@testing-library/user-event": "^14.4.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}