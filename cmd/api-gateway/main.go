package main

import (
	"context"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/spf13/viper"
	"github.com/workflowmaster/workflowmaster/internal/api"
	"github.com/workflowmaster/workflowmaster/internal/database"
	"github.com/workflowmaster/workflowmaster/internal/scheduler"
	"github.com/workflowmaster/workflowmaster/internal/workflow"
)

func main() {
	// Load configuration
	config := loadConfig()

	// Initialize database
	db, err := database.NewDatabase(database.Config{
		Host:     config.GetString("database.host"),
		Port:     config.GetInt("database.port"),
		User:     config.GetString("database.user"),
		Password: config.GetString("database.password"),
		DBName:   config.GetString("database.name"),
		SSLMode:  config.GetString("database.sslmode"),
		Timezone: config.GetString("database.timezone"),
	})
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	// Run migrations
	if config.GetBool("database.auto_migrate") {
		if err := db.AutoMigrate(); err != nil {
			log.Fatalf("Failed to run migrations: %v", err)
		}
	}

	// Seed initial data
	if config.GetBool("database.seed_data") {
		if err := db.SeedData(); err != nil {
			log.Printf("Warning: Failed to seed data: %v", err)
		}
	}

	// Initialize scheduler
	schedulerService := scheduler.NewScheduler(db)
	if err := schedulerService.Start(); err != nil {
		log.Fatalf("Failed to start scheduler: %v", err)
	}
	defer schedulerService.Stop()

	// Initialize workflow engine
	workflowEngine := workflow.NewWorkflowEngine(db)
	if err := workflowEngine.Start(); err != nil {
		log.Fatalf("Failed to start workflow engine: %v", err)
	}
	defer workflowEngine.Stop()

	// Initialize API server
	server := api.NewServer(api.Config{
		Host:         config.GetString("server.host"),
		Port:         config.GetInt("server.port"),
		Debug:        config.GetBool("server.debug"),
		CORSOrigins:  config.GetStringSlice("server.cors_origins"),
		JWTSecret:    config.GetString("auth.jwt_secret"),
		ReadTimeout:  config.GetDuration("server.read_timeout"),
		WriteTimeout: config.GetDuration("server.write_timeout"),
	}, db, schedulerService, workflowEngine)

	// Start server in a goroutine
	go func() {
		if err := server.Start(); err != nil {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("Shutting down server...")

	// Create a deadline for shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Shutdown server
	if err := server.Stop(ctx); err != nil {
		log.Printf("Server forced to shutdown: %v", err)
	}

	log.Println("Server exited")
}

func loadConfig() *viper.Viper {
	config := viper.New()

	// Set default values
	config.SetDefault("server.host", "0.0.0.0")
	config.SetDefault("server.port", 8080)
	config.SetDefault("server.debug", false)
	config.SetDefault("server.read_timeout", "30s")
	config.SetDefault("server.write_timeout", "30s")
	config.SetDefault("server.cors_origins", []string{"*"})

	config.SetDefault("database.host", "localhost")
	config.SetDefault("database.port", 5432)
	config.SetDefault("database.user", "workflowmaster")
	config.SetDefault("database.password", "password")
	config.SetDefault("database.name", "workflowmaster")
	config.SetDefault("database.sslmode", "disable")
	config.SetDefault("database.timezone", "UTC")
	config.SetDefault("database.auto_migrate", true)
	config.SetDefault("database.seed_data", true)

	config.SetDefault("auth.jwt_secret", "your-secret-key-change-this-in-production")

	// Set config file name and paths
	config.SetConfigName("config")
	config.SetConfigType("yaml")
	config.AddConfigPath(".")
	config.AddConfigPath("./configs")
	config.AddConfigPath("/etc/workflowmaster")

	// Enable environment variable support
	config.AutomaticEnv()
	config.SetEnvPrefix("WM")

	// Read config file
	if err := config.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); ok {
			log.Println("No config file found, using defaults and environment variables")
		} else {
			log.Printf("Error reading config file: %v", err)
		}
	} else {
		log.Printf("Using config file: %s", config.ConfigFileUsed())
	}

	return config
}
