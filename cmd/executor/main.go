package main

import (
	"log"
	"os"
	"os/signal"
	"syscall"

	"github.com/google/uuid"
	"github.com/spf13/viper"
	"github.com/workflowmaster/workflowmaster/internal/database"
	"github.com/workflowmaster/workflowmaster/internal/executor"
)

func main() {
	// Load configuration
	config := loadConfig()

	// Initialize database
	db, err := database.NewDatabase(database.Config{
		Host:     config.GetString("database.host"),
		Port:     config.GetInt("database.port"),
		User:     config.GetString("database.user"),
		Password: config.GetString("database.password"),
		DBName:   config.GetString("database.name"),
		SSLMode:  config.GetString("database.sslmode"),
		Timezone: config.GetString("database.timezone"),
	})
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	// Generate or get node ID
	nodeID := uuid.New()
	if config.IsSet("executor.node_id") {
		if id, err := uuid.Parse(config.GetString("executor.node_id")); err == nil {
			nodeID = id
		}
	}

	// Initialize executor
	executorService := executor.NewExecutor(
		db,
		nodeID,
		config.GetInt("executor.max_concurrent_jobs"),
	)

	if err := executorService.Start(); err != nil {
		log.Fatalf("Failed to start executor: %v", err)
	}
	defer executorService.Stop()

	log.Printf("Executor service started successfully (Node ID: %s)", nodeID)

	// Wait for interrupt signal
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("Shutting down executor service...")
}

func loadConfig() *viper.Viper {
	config := viper.New()

	// Set default values
	config.SetDefault("database.host", "localhost")
	config.SetDefault("database.port", 5432)
	config.SetDefault("database.user", "workflowmaster")
	config.SetDefault("database.password", "password")
	config.SetDefault("database.name", "workflowmaster")
	config.SetDefault("database.sslmode", "disable")
	config.SetDefault("database.timezone", "UTC")

	config.SetDefault("executor.max_concurrent_jobs", 5)

	// Set config file name and paths
	config.SetConfigName("config")
	config.SetConfigType("yaml")
	config.AddConfigPath(".")
	config.AddConfigPath("./configs")
	config.AddConfigPath("/etc/workflowmaster")

	// Enable environment variable support
	config.AutomaticEnv()
	config.SetEnvPrefix("WM")

	// Read config file
	if err := config.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); ok {
			log.Println("No config file found, using defaults and environment variables")
		} else {
			log.Printf("Error reading config file: %v", err)
		}
	}

	return config
}
