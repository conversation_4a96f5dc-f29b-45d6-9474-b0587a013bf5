package main

import (
	"log"
	"os"
	"os/signal"
	"syscall"

	"github.com/spf13/viper"
	"github.com/workflowmaster/workflowmaster/internal/database"
	"github.com/workflowmaster/workflowmaster/internal/scheduler"
)

func main() {
	// Load configuration
	config := loadConfig()

	// Initialize database
	db, err := database.NewDatabase(database.Config{
		Host:     config.GetString("database.host"),
		Port:     config.GetInt("database.port"),
		User:     config.GetString("database.user"),
		Password: config.GetString("database.password"),
		DBName:   config.GetString("database.name"),
		SSLMode:  config.GetString("database.sslmode"),
		Timezone: config.GetString("database.timezone"),
	})
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	// Initialize scheduler
	schedulerService := scheduler.NewScheduler(db)
	if err := schedulerService.Start(); err != nil {
		log.Fatalf("Failed to start scheduler: %v", err)
	}
	defer schedulerService.Stop()

	log.Println("Scheduler service started successfully")

	// Wait for interrupt signal
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("Shutting down scheduler service...")
}

func loadConfig() *viper.Viper {
	config := viper.New()

	// Set default values
	config.SetDefault("database.host", "localhost")
	config.SetDefault("database.port", 5432)
	config.SetDefault("database.user", "workflowmaster")
	config.SetDefault("database.password", "password")
	config.SetDefault("database.name", "workflowmaster")
	config.SetDefault("database.sslmode", "disable")
	config.SetDefault("database.timezone", "UTC")

	// Set config file name and paths
	config.SetConfigName("config")
	config.SetConfigType("yaml")
	config.AddConfigPath(".")
	config.AddConfigPath("./configs")
	config.AddConfigPath("/etc/workflowmaster")

	// Enable environment variable support
	config.AutomaticEnv()
	config.SetEnvPrefix("WM")

	// Read config file
	if err := config.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); ok {
			log.Println("No config file found, using defaults and environment variables")
		} else {
			log.Printf("Error reading config file: %v", err)
		}
	}

	return config
}
