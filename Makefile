# WorkflowMaster Makefile

# Variables
BINARY_NAME=workflowmaster
DOCKER_REGISTRY=workflowmaster
VERSION?=latest
GOVERSION=$(shell go version)
GOOS=$(shell go env GOOS)
GOARCH=$(shell go env GOARCH)

# Build info
BUILD_TIME=$(shell date -u '+%Y-%m-%d_%H:%M:%S')
GIT_COMMIT=$(shell git rev-parse HEAD)
GIT_BRANCH=$(shell git rev-parse --abbrev-ref HEAD)

# Linker flags
LDFLAGS=-ldflags "-X main.Version=${VERSION} -X main.BuildTime=${BUILD_TIME} -X main.GitCommit=${GIT_COMMIT} -X main.GitBranch=${GIT_BRANCH}"

.PHONY: help
help: ## Display this help screen
	@grep -h -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}'

.PHONY: deps
deps: ## Download dependencies
	go mod download
	go mod tidy

.PHONY: build
build: deps ## Build all services
	@echo "Building WorkflowMaster services..."
	go build ${LDFLAGS} -o bin/scheduler ./cmd/scheduler
	go build ${LDFLAGS} -o bin/executor ./cmd/executor
	go build ${LDFLAGS} -o bin/api-gateway ./cmd/api-gateway
	go build ${LDFLAGS} -o bin/auth-service ./cmd/auth-service
	go build ${LDFLAGS} -o bin/workflow-service ./cmd/workflow-service
	go build ${LDFLAGS} -o bin/monitor-service ./cmd/monitor-service
	go build ${LDFLAGS} -o bin/cli ./cmd/cli

.PHONY: build-linux
build-linux: ## Build for Linux
	GOOS=linux GOARCH=amd64 go build ${LDFLAGS} -o bin/linux/scheduler ./cmd/scheduler
	GOOS=linux GOARCH=amd64 go build ${LDFLAGS} -o bin/linux/executor ./cmd/executor
	GOOS=linux GOARCH=amd64 go build ${LDFLAGS} -o bin/linux/api-gateway ./cmd/api-gateway
	GOOS=linux GOARCH=amd64 go build ${LDFLAGS} -o bin/linux/auth-service ./cmd/auth-service
	GOOS=linux GOARCH=amd64 go build ${LDFLAGS} -o bin/linux/workflow-service ./cmd/workflow-service
	GOOS=linux GOARCH=amd64 go build ${LDFLAGS} -o bin/linux/monitor-service ./cmd/monitor-service

.PHONY: test
test: ## Run tests
	go test -v -race -coverprofile=coverage.out ./...

.PHONY: test-coverage
test-coverage: test ## Run tests with coverage report
	go tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

.PHONY: lint
lint: ## Run linter
	golangci-lint run

.PHONY: fmt
fmt: ## Format code
	go fmt ./...
	goimports -w .

.PHONY: clean
clean: ## Clean build artifacts
	rm -rf bin/
	rm -f coverage.out coverage.html

.PHONY: docker-build
docker-build: ## Build Docker images
	docker build -t ${DOCKER_REGISTRY}/scheduler:${VERSION} -f deployments/docker/Dockerfile.scheduler .
	docker build -t ${DOCKER_REGISTRY}/executor:${VERSION} -f deployments/docker/Dockerfile.executor .
	docker build -t ${DOCKER_REGISTRY}/api-gateway:${VERSION} -f deployments/docker/Dockerfile.api-gateway .
	docker build -t ${DOCKER_REGISTRY}/auth-service:${VERSION} -f deployments/docker/Dockerfile.auth-service .
	docker build -t ${DOCKER_REGISTRY}/workflow-service:${VERSION} -f deployments/docker/Dockerfile.workflow-service .
	docker build -t ${DOCKER_REGISTRY}/monitor-service:${VERSION} -f deployments/docker/Dockerfile.monitor-service .
	docker build -t ${DOCKER_REGISTRY}/web-ui:${VERSION} -f deployments/docker/Dockerfile.web-ui .

.PHONY: docker-push
docker-push: ## Push Docker images
	docker push ${DOCKER_REGISTRY}/scheduler:${VERSION}
	docker push ${DOCKER_REGISTRY}/executor:${VERSION}
	docker push ${DOCKER_REGISTRY}/api-gateway:${VERSION}
	docker push ${DOCKER_REGISTRY}/auth-service:${VERSION}
	docker push ${DOCKER_REGISTRY}/workflow-service:${VERSION}
	docker push ${DOCKER_REGISTRY}/monitor-service:${VERSION}
	docker push ${DOCKER_REGISTRY}/web-ui:${VERSION}

.PHONY: dev-infra
dev-infra: ## Start development infrastructure
	docker-compose -f deployments/docker-compose.dev.yml up -d

.PHONY: dev-infra-down
dev-infra-down: ## Stop development infrastructure
	docker-compose -f deployments/docker-compose.dev.yml down

.PHONY: dev-backend
dev-backend: dev-infra ## Start backend services in development mode
	@echo "Starting backend services..."
	go run ./cmd/scheduler &
	go run ./cmd/executor &
	go run ./cmd/api-gateway &
	go run ./cmd/auth-service &
	go run ./cmd/workflow-service &
	go run ./cmd/monitor-service &
	wait

.PHONY: dev-frontend
dev-frontend: ## Start frontend in development mode
	cd web && npm install && npm run dev

.PHONY: migrate-up
migrate-up: ## Run database migrations
	migrate -path migrations -database "postgres://workflowmaster:password@localhost:5432/workflowmaster?sslmode=disable" up

.PHONY: migrate-down
migrate-down: ## Rollback database migrations
	migrate -path migrations -database "postgres://workflowmaster:password@localhost:5432/workflowmaster?sslmode=disable" down

.PHONY: migrate-create
migrate-create: ## Create new migration (usage: make migrate-create NAME=migration_name)
	migrate create -ext sql -dir migrations -seq $(NAME)

.PHONY: proto
proto: ## Generate protobuf files
	protoc --go_out=. --go_opt=paths=source_relative \
		--go-grpc_out=. --go-grpc_opt=paths=source_relative \
		api/proto/*.proto

.PHONY: openapi
openapi: ## Generate OpenAPI documentation
	swag init -g cmd/api-gateway/main.go -o api/openapi

.PHONY: install-tools
install-tools: ## Install development tools
	go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	go install golang.org/x/tools/cmd/goimports@latest
	go install github.com/swaggo/swag/cmd/swag@latest
	go install google.golang.org/protobuf/cmd/protoc-gen-go@latest
	go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest

.PHONY: k8s-deploy
k8s-deploy: ## Deploy to Kubernetes
	kubectl apply -f deployments/k8s/

.PHONY: k8s-delete
k8s-delete: ## Delete from Kubernetes
	kubectl delete -f deployments/k8s/

.PHONY: helm-install
helm-install: ## Install using Helm
	helm install workflowmaster ./deployments/helm/workflowmaster

.PHONY: helm-upgrade
helm-upgrade: ## Upgrade using Helm
	helm upgrade workflowmaster ./deployments/helm/workflowmaster

.PHONY: helm-uninstall
helm-uninstall: ## Uninstall using Helm
	helm uninstall workflowmaster

.PHONY: all
all: deps fmt lint test build ## Run all checks and build
