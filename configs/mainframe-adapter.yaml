# WorkflowMaster Mainframe Adapter Configuration

# Main adapter settings
adapter:
  enabled: true
  max_connections: 50
  connection_timeout: 30s
  job_polling_interval: 30s
  monitoring_interval: 60s
  retry_attempts: 3
  retry_delay: 30s
  enable_ssl: true
  ssl_verify_peer: true
  log_level: "info"

# Security settings
security:
  encryption_key: "${MAINFRAME_ENCRYPTION_KEY}"
  certificate_path: "/etc/workflowmaster/certs/mainframe.crt"
  private_key_path: "/etc/workflowmaster/certs/mainframe.key"
  trusted_ca_path: "/etc/workflowmaster/certs/ca.crt"

# Protocol-specific configurations
protocols:
  ftp:
    passive_mode: true
    binary_mode: true
    timeout: 30s
    keep_alive: 60s
    
  sftp:
    key_exchanges:
      - "diffie-hellman-group14-sha256"
      - "diffie-hellman-group14-sha1"
      - "ecdh-sha2-nistp256"
    ciphers:
      - "aes128-ctr"
      - "aes192-ctr"
      - "aes256-ctr"
      - "<EMAIL>"
    macs:
      - "hmac-sha2-256"
      - "hmac-sha2-512"
      - "hmac-sha1"
    timeout: 30s
    
  mq:
    queue_manager: "QM1"
    channel: "SYSTEM.DEF.SVRCONN"
    conn_name: "localhost(1414)"
    user_id: ""
    timeout: 30s
    
  telnet:
    login_prompt: "login:"
    password_prompt: "Password:"
    command_prompt: "$ "
    timeout: 30s

# Connection profiles for different environments
connection_profiles:
  development:
    - name: "dev-zos-01"
      description: "Development z/OS System"
      environment: "development"
      system_type: "zos"
      hostname: "dev-zos.company.com"
      port: 23
      username: "devuser"
      auth_method: "password"
      security_system: "RACF"
      timeout: 30
      max_connections: 5
      protocol_settings:
        protocol: "telnet"
        
  test:
    - name: "test-zos-01"
      description: "Test z/OS System"
      environment: "test"
      system_type: "zos"
      hostname: "test-zos.company.com"
      port: 22
      username: "testuser"
      auth_method: "key"
      security_system: "RACF"
      timeout: 30
      max_connections: 10
      use_ssl: true
      protocol_settings:
        protocol: "sftp"
        
  production:
    - name: "prod-zos-01"
      description: "Production z/OS System - Primary"
      environment: "production"
      system_type: "zos"
      hostname: "prod-zos-01.company.com"
      port: 22
      username: "produser"
      auth_method: "key"
      security_system: "RACF"
      timeout: 30
      max_connections: 20
      use_ssl: true
      ssl_cert_path: "/etc/workflowmaster/certs/prod-zos-01.crt"
      ssl_key_path: "/etc/workflowmaster/certs/prod-zos-01.key"
      protocol_settings:
        protocol: "sftp"
        
    - name: "prod-zos-02"
      description: "Production z/OS System - Secondary"
      environment: "production"
      system_type: "zos"
      hostname: "prod-zos-02.company.com"
      port: 22
      username: "produser"
      auth_method: "key"
      security_system: "RACF"
      timeout: 30
      max_connections: 20
      use_ssl: true
      ssl_cert_path: "/etc/workflowmaster/certs/prod-zos-02.crt"
      ssl_key_path: "/etc/workflowmaster/certs/prod-zos-02.key"
      protocol_settings:
        protocol: "sftp"

# Job execution settings
job_execution:
  default_job_class: "A"
  default_timeout: 3600
  max_retry_attempts: 3
  retry_delay: 300
  enable_job_monitoring: true
  job_log_retention_days: 30
  
  # JCL template settings
  jcl_templates:
    default_job_card: "//{{JOB_NAME}} JOB CLASS={{JOB_CLASS}},MSGCLASS=X,NOTIFY=&SYSUID"
    default_step_template: "//{{STEP_NAME}} EXEC PGM={{PROGRAM_NAME}}"
    
  # Job classes and their characteristics
  job_classes:
    A:
      description: "High priority batch jobs"
      max_concurrent: 5
      cpu_limit: "unlimited"
      memory_limit: "2GB"
      
    B:
      description: "Medium priority batch jobs"
      max_concurrent: 10
      cpu_limit: "30min"
      memory_limit: "1GB"
      
    C:
      description: "Low priority batch jobs"
      max_concurrent: 20
      cpu_limit: "10min"
      memory_limit: "512MB"

# Data transfer settings
data_transfer:
  default_transfer_mode: "binary"
  enable_encoding_conversion: true
  default_source_encoding: "EBCDIC"
  default_target_encoding: "UTF-8"
  transfer_timeout: 1800
  max_file_size_mb: 1024
  
  # Encoding mappings
  encoding_mappings:
    ebcdic_variants:
      - "EBCDIC"
      - "CP037"
      - "CP500"
      - "CP1047"
    ascii_variants:
      - "ASCII"
      - "UTF-8"
      - "ISO-8859-1"
      - "WINDOWS-1252"

# Monitoring settings
monitoring:
  enable_real_time: true
  collection_interval: 60s
  retention_days: 90
  
  # Metric types to collect
  metric_types:
    - "system_resources"
    - "job_queue"
    - "database"
    - "network"
    - "storage"
    
  # Alert thresholds
  alert_thresholds:
    cpu_utilization_percent: 80
    memory_utilization_percent: 85
    job_queue_depth: 100
    response_time_seconds: 30
    error_rate_percent: 5
    
  # Integration with monitoring tools
  integrations:
    prometheus:
      enabled: true
      endpoint: "http://prometheus:9090"
      push_interval: 30s
      
    grafana:
      enabled: true
      dashboard_url: "http://grafana:3000/d/mainframe"
      
    sysview:
      enabled: false
      host: "sysview.company.com"
      port: 3270
      
    omegamon:
      enabled: false
      host: "omegamon.company.com"
      port: 1918

# Security system configurations
security_systems:
  racf:
    profile_prefix: "SYS1"
    group_prefix: "GRP"
    permission_class: "DATASET"
    default_group: "USERS"
    
  acf2:
    logon_id: "ACF2"
    default_lid: "USER"
    security_level: "1"
    
  top_secret:
    acid: "TSS"
    department: "IT"
    security_level: "1"

# Notification settings
notifications:
  email:
    smtp_server: "smtp.company.com"
    smtp_port: 587
    use_tls: true
    from_address: "<EMAIL>"
    
  slack:
    webhook_url: "${SLACK_WEBHOOK_URL}"
    channel: "#mainframe-alerts"
    
  webhook:
    timeout: 30s
    retry_attempts: 3
    
  mq:
    queue_manager: "QM1"
    notification_queue: "WORKFLOWMASTER.NOTIFICATIONS"

# Copybook settings
copybooks:
  default_encoding: "EBCDIC"
  cache_parsed_structures: true
  cache_ttl: 3600s
  
  # Common copybook libraries
  libraries:
    - name: "PROD.COPYBOOK.LIBRARY"
      description: "Production copybook library"
      path: "PROD.COPYBOOK.LIBRARY"
      
    - name: "TEST.COPYBOOK.LIBRARY"
      description: "Test copybook library"
      path: "TEST.COPYBOOK.LIBRARY"

# Dataset management
datasets:
  # Default dataset attributes
  defaults:
    record_format: "FB"
    record_length: 80
    block_size: 8000
    space_unit: "CYL"
    primary_space: 10
    secondary_space: 5
    
  # Dataset naming conventions
  naming_conventions:
    work_prefix: "WORK"
    temp_prefix: "TEMP"
    backup_prefix: "BACKUP"
    date_format: "YYYYMMDD"
    
  # Cleanup policies
  cleanup:
    auto_cleanup_work_datasets: true
    work_dataset_retention_days: 7
    temp_dataset_retention_hours: 24
    backup_dataset_retention_days: 30

# Performance tuning
performance:
  connection_pool_size: 10
  connection_pool_timeout: 30s
  job_submission_batch_size: 5
  parallel_job_limit: 20
  
  # Caching settings
  cache:
    job_definitions: true
    connection_metadata: true
    security_profiles: true
    ttl: 1800s

# Logging configuration
logging:
  level: "info"
  format: "json"
  output: "stdout"
  
  # Log rotation
  rotation:
    max_size: "100MB"
    max_files: 10
    max_age: "30d"
    
  # Audit logging
  audit:
    enabled: true
    include_sensitive_data: false
    retention_days: 365

# High availability settings
high_availability:
  enabled: true
  health_check_interval: 30s
  failover_timeout: 60s
  
  # Load balancing
  load_balancing:
    strategy: "round_robin"  # round_robin, least_connections, weighted
    health_check_path: "/health"
    
  # Clustering
  clustering:
    enabled: false
    cluster_name: "workflowmaster-mainframe"
    discovery_method: "static"  # static, consul, etcd
    nodes:
      - "workflowmaster-01:8080"
      - "workflowmaster-02:8080"

# Development and testing
development:
  mock_mainframe: false
  simulate_delays: false
  debug_mode: false
  
  # Test data
  test_datasets:
    - "TEST.CUSTOMER.DATA"
    - "TEST.TRANSACTION.DATA"
    - "TEST.REFERENCE.DATA"
